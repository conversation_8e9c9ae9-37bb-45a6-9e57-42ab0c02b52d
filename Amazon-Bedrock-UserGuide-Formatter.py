#!/usr/bin/env python3
"""
Amazon Bedrock User Guide Formatter
Automated script to clean up formatting issues in the Amazon Bedrock User Guide.
"""

import re
import os
from typing import List, Tuple

def read_file(file_path: str) -> str:
    """Read the content of the input file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def write_file(file_path: str, content: str) -> None:
    """Write content to the output file."""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def get_file_stats(file_path: str) -> Tuple[int, int]:
    """Get file statistics (line count, file size in bytes)."""
    if not os.path.exists(file_path):
        return 0, 0
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = len(f.readlines())
    
    size = os.path.getsize(file_path)
    return lines, size

def remove_isolated_page_numbers(content: str) -> str:
    """Remove isolated page numbers in code blocks."""
    # Pattern: ```\n(single number)\n``` (standalone page numbers)
    pattern = r'```\n(\d+)\n```'
    return re.sub(pattern, '', content)

def convert_notes_to_blockquotes(content: str) -> str:
    """Convert note/warning blocks from code format to blockquote format."""
    # Pattern for notes in code blocks
    patterns = [
        (r'```\n(Note|Warning|Important|Tip)\n(.*?)\n```', r'> **\1**\n> \2'),
        (r'```\n(Note|Warning|Important|Tip)\n(.*?)\n(.*?)\n```', r'> **\1**\n> \2\n> \3'),
        # Multi-line notes
        (r'```\n(Note|Warning|Important|Tip)(?:\s*\n)+((?:(?!```).*\n?)*?)```', 
         lambda m: f'> **{m.group(1)}**\n' + '\n'.join(f'> {line}' for line in m.group(2).strip().split('\n') if line.strip())),
    ]
    
    for pattern, replacement in patterns:
        if callable(replacement):
            content = re.sub(pattern, replacement, content, flags=re.DOTALL | re.MULTILINE)
        else:
            content = re.sub(pattern, replacement, content, flags=re.DOTALL | re.MULTILINE)
    
    return content

def consolidate_consecutive_code_blocks(content: str) -> str:
    """Consolidate consecutive code blocks that should be combined."""
    # Pattern: ``` followed by ``` with only whitespace/newlines between
    pattern = r'```\n\n+```'
    return re.sub(pattern, '\n', content)

def remove_excessive_empty_lines(content: str) -> str:
    """Remove excessive empty lines (keep max 1 blank line between sections)."""
    # Replace 3+ consecutive newlines with 2 newlines (1 blank line)
    pattern = r'\n{3,}'
    return re.sub(pattern, '\n\n', content)

def fix_fragmented_table_cells(content: str) -> str:
    """Fix fragmented table cells that are in individual code blocks."""
    # This is complex and would need specific patterns based on actual content
    # For now, we'll handle simple cases where table content is wrapped in code blocks
    
    # Pattern for simple table fragments in code blocks
    table_patterns = [
        # Single cell content in code blocks
        (r'```\n(\|[^|]*\|)\n```', r'\1'),
        # Header separators in code blocks
        (r'```\n(\|[-:\s|]+\|)\n```', r'\1'),
    ]
    
    for pattern, replacement in table_patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    return content

def remove_repetitive_headers(content: str) -> str:
    """Remove repetitive section headers with page numbers."""
    # Pattern for headers followed by page numbers
    patterns = [
        # Section title followed by page number on separate line
        (r'\n([A-Z][^.\n]+) (\d+)\n\n', r'\n\1\n\n'),
        # Repetitive section titles (same title appearing multiple times)
        (r'\n(#+\s*[^\n]+)\n+\1\n+', r'\n\1\n\n'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    return content

def fix_broken_formatting(content: str) -> str:
    """Fix other broken formatting issues."""
    fixes = [
        # Fix broken numbered lists that got split
        (r'\n(\d+)\.\n([A-Z][^.]+)', r'\n\1. \2'),
        # Fix broken bullet points
        (r'\n-\n([A-Z][^.]+)', r'\n- \1'),
        # Remove trailing whitespace
        (r'[ \t]+$', '', re.MULTILINE),
        # Fix double periods in headings
        (r'(#+\s*[^.\n]+)\.\.(\n)', r'\1.\2'),
    ]
    
    for pattern, replacement, *flags in fixes:
        flags = flags[0] if flags else 0
        content = re.sub(pattern, replacement, content, flags=flags)
    
    return content

def clean_up_aws_references(content: str) -> str:
    """Clean up AWS-specific formatting issues."""
    # Fix AWS service names and references
    fixes = [
        # Fix broken AWS service names
        (r'AWS\s+([A-Z][a-z]+)', r'AWS \1'),
        # Fix broken console references
        (r'AWS\s+Management\s+Console', r'AWS Management Console'),
    ]
    
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content)
    
    return content

def apply_all_optimizations(content: str) -> str:
    """Apply all formatting optimizations."""
    print("Applying formatting optimizations...")
    
    # Order matters - some fixes depend on others
    optimizations = [
        ("Removing isolated page numbers", remove_isolated_page_numbers),
        ("Converting notes to blockquotes", convert_notes_to_blockquotes),
        ("Consolidating consecutive code blocks", consolidate_consecutive_code_blocks),
        ("Fixing fragmented table cells", fix_fragmented_table_cells),
        ("Removing repetitive headers", remove_repetitive_headers),
        ("Fixing broken formatting", fix_broken_formatting),
        ("Cleaning up AWS references", clean_up_aws_references),
        ("Removing excessive empty lines", remove_excessive_empty_lines),  # This should be last
    ]
    
    for description, optimization_func in optimizations:
        print(f"  - {description}...")
        content = optimization_func(content)
    
    return content

def main():
    """Main function to process the Amazon Bedrock User Guide."""
    input_file = "/Users/<USER>/localDev/trialynx-insights/Amazon-Bedrock-UserGuide.md"
    output_file = "/Users/<USER>/localDev/trialynx-insights/Amazon-Bedrock-UserGuide-Optimized.md"
    
    print("Amazon Bedrock User Guide Formatter")
    print("=" * 50)
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found!")
        return
    
    # Get original file statistics
    original_lines, original_size = get_file_stats(input_file)
    print(f"Original file: {original_lines:,} lines, {original_size:,} bytes ({original_size/1024/1024:.1f} MB)")
    
    # Read and process content
    print("\nReading input file...")
    content = read_file(input_file)
    
    # Apply optimizations
    optimized_content = apply_all_optimizations(content)
    
    # Write optimized content
    print("\nWriting optimized file...")
    write_file(output_file, optimized_content)
    
    # Get optimized file statistics
    optimized_lines, optimized_size = get_file_stats(output_file)
    print(f"Optimized file: {optimized_lines:,} lines, {optimized_size:,} bytes ({optimized_size/1024/1024:.1f} MB)")
    
    # Calculate savings
    line_reduction = original_lines - optimized_lines
    size_reduction = original_size - optimized_size
    line_reduction_pct = (line_reduction / original_lines * 100) if original_lines > 0 else 0
    size_reduction_pct = (size_reduction / original_size * 100) if original_size > 0 else 0
    
    print("\n" + "=" * 50)
    print("OPTIMIZATION RESULTS:")
    print(f"Lines reduced:     {line_reduction:,} ({line_reduction_pct:.1f}%)")
    print(f"Size reduced:      {size_reduction:,} bytes ({size_reduction_pct:.1f}%)")
    print(f"Final size:        {optimized_size/1024/1024:.1f} MB")
    print("=" * 50)
    
    if line_reduction > 0 or size_reduction > 0:
        print("✅ Optimization complete! Check the optimized file:")
        print(f"   {output_file}")
    else:
        print("ℹ️  No significant optimizations were applied.")

if __name__ == "__main__":
    main()