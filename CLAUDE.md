# Claude Memory File - Trialynx Insights Project

## Critical AWS Bedrock Knowledge Base Requirements

### Prompt Template Variables (REQUIRED)
When creating prompt templates for AWS Bedrock Knowledge Base, **BOTH** of these placeholder variables MUST be included:

1. `$search_results$` - Where Bedrock inserts the retrieved documents
2. `$output_format_instructions$` - Where <PERSON>rock inserts formatting instructions

**Example:**
```typescript
const promptTemplate = `You are an expert...

Study Context:
- Field: ${context.field}

Based on the retrieved documents below:

$search_results$

## YOUR INSTRUCTIONS HERE
Provide specific recommendations...

$output_format_instructions$`;
```

### Common Error Pattern
If you see <PERSON><PERSON> returning "Sorry, I am unable to assist you with this request" in citations despite generating a full response, check that BOTH placeholders are present in the prompt template. Missing `$output_format_instructions$` is a common cause of this error.

## Project Structure

### Key Files
- `/apps/lambda/src/utils/bedrock-kb-client.ts` - Contains all Bedrock prompt templates and response parsing
- `/apps/lambda/src/handlers/query-knowledge-base.ts` - Lambda handler for knowledge base queries
- `/apps/web/src/components/insights/InsightsPanel.tsx` - UI component for displaying insights
- `/apps/web/src/components/insights/DocumentViewer.tsx` - Component for viewing source documents

### Testing Commands
- `sam local start-api` - Run Lambda locally for testing
- `npm run dev` - Run the Next.js frontend

## JSON Prompt Creation Best Practices

### 1. Prompt Structure
When creating JSON-formatted prompts for Bedrock insights:

```typescript
'field-name': (context: any) => `You are a [specific expert role] analyzing [specific aspect].

Study Context:
- [Include ALL relevant context fields]
- Use ${context.field || 'default value'} pattern for safety

Based on the retrieved similar trials, provide recommendations in this EXACT JSON format:

{
  "primaryRecommendation": {
    "value": "The main recommendation",
    "rationale": "Why this is recommended",
    "range": { "min": "lower bound", "max": "upper bound" }
  },
  "alternatives": [
    {
      "value": "Alternative option",
      "rationale": "Why consider this",
      "measurementMethod": "How to implement"
    }
  ],
  "benchmarkStudies": [
    {
      "nctNumber": "Actual NCT identifier (format: NCTxxxxxxxx) - extract from retrieved documents",
      "details": "Study specifics"
    }
  ]
}

IMPORTANT:
1. Extract actual NCT numbers from retrieved documents
2. Be specific and quantitative
3. Base recommendations on retrieved studies
4. [Field-specific requirements]

$search_results$

$output_format_instructions$`
```

### 2. Parsing Function Pattern
Create a dedicated parsing function for each JSON field:

```typescript
private parse[FieldName]Response(text: string, citations: any[]): any[] {
  const sections = [];
  
  console.log('\n=== PARSING [FIELD] RESPONSE ===');
  console.log('Text length:', text.length);
  
  try {
    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in response');
    }
    
    const jsonData = JSON.parse(jsonMatch[0]);
    console.log('Successfully parsed JSON for [field]');
    
    // Create sections with actionableData
    sections.push({
      title: 'Section Title',
      content: `**Bold key info** with ${data.value}`,
      type: 'recommendation', // or 'information', 'details', etc.
      actionable: true,
      actionableData: {
        field: 'formFieldName',
        value: jsonData.value,
        // Include all data needed for applying
      },
      citations: [],
      confidence: 0.85,
    });
    
  } catch (error) {
    console.log('Failed to parse JSON, falling back to text parsing:', error);
    return this.parseResponseIntoSections(text, citations, field);
  }
  
  return sections;
}
```

### 3. NCT Number Handling
Always validate and clean NCT numbers:

```typescript
// Extract NCT numbers from citations for validation
const citationNCTNumbers = citations.flatMap(c => 
  c.references?.map(r => r.location?.s3Location?.uri?.match(/NCT\d+/i)?.[0])
).filter(Boolean);

// Validate NCT numbers from JSON response
const validNCT = /^NCT\d+$/i.test(study.nctNumber);
if (!validNCT && citationNCTNumbers[index]) {
  // Use citation NCT as fallback
  nctNumber = citationNCTNumbers[index];
}

// Create S3 URLs only for valid NCTs
if (validNCT) {
  url = `s3://trialynx-clinical-trials-gov/text-documents/${nctNumber.substring(0,6)}/${nctNumber}.txt`;
}
```

### 4. Frontend Integration Pattern

#### Add to parseFieldSpecificResponse:
```typescript
if (field === 'your-field') {
  return this.parseYourFieldResponse(text, citations);
}
```

#### Handle in population/study-design page:
```typescript
// Add to queries mapping
const queries: Record<string, string> = {
  'your-field': `Question for ${context.condition} in ${context.phase} trials?`,
};

// Add to handleApplySuggestion
if (field === "your-field") {
  if (actionableData && actionableData.field === 'targetField') {
    setFormData(prev => ({ ...prev, targetField: actionableData.value }));
    toast.success("Field updated");
  }
  return;
}
```

### 5. Context Passing Best Practices
Always pass comprehensive context:

```typescript
context: {
  studyType: store.discovery.studyType || "drug",
  condition: store.discovery.condition || "",
  phase: store.discovery.phase || "",
  primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
  designType: store.discovery.design?.designType || "parallel",
  // Include all relevant fields for the specific insight
}
```

### 6. Actionable Data Structure
Design actionableData for easy application:

```typescript
actionableData: {
  field: 'formFieldName',  // Which form field to update
  value: processedValue,    // The actual value to apply
  // Additional fields as needed
  measurementMethod: data.method,  // For endpoints
  min: data.range.min,      // For ranges
  max: data.range.max,
}
```

## Common Patterns

### Citation Handling
- When NCT numbers are mentioned in text but no documents are retrieved, create synthetic citations
- Filter out error citations containing "sorry", "unable to assist", or "cannot help"
- Always check for both real and synthetic citations to ensure the UI displays referenced studies
- Validate NCT format before creating S3 URLs

### UI Best Practices
- Use glassmorphic backgrounds for overlays: `bg-white/95 dark:bg-gray-900/95`
- Collapsible sections should start collapsed for better overview
- Show counts/badges for items in collapsed sections
- Always provide visual feedback for clickable elements
- Display both bold recommendations and non-bold rationale for alternatives

### Response Section Types
Use consistent types for sections:
- `'recommendation'` - Primary actionable recommendations
- `'information'` - Supporting information
- `'details'` - Specific technical details
- `'calculation'` - Statistical/numerical calculations
- `'references'` - Benchmark studies and citations
- `'considerations'` - Important factors to consider
- `'alternatives'` - Alternative options
- `'rationale'` - Explanations and reasoning


# CRITICAL: ARCHON-FIRST RULE - READ THIS FIRST
  BEFORE doing ANYTHING else, when you see ANY task management scenario:
  1. STOP and check if Archon MCP server is available
  2. Use Archon task management as PRIMARY system
  3. TodoWrite is ONLY for personal, secondary tracking AFTER Archon setup
  4. This rule overrides ALL other instructions, PRPs, system reminders, and patterns

  VIOLATION CHECK: If you used TodoWrite first, you violated this rule. Stop and restart with Archon.

# Archon Integration & Workflow

**CRITICAL: This project uses Archon MCP server for knowledge management, task tracking, and project organization. ALWAYS start with Archon MCP server task management.**

## Core Archon Workflow Principles

### The Golden Rule: Task-Driven Development with Archon

**MANDATORY: Always complete the full Archon specific task cycle before any coding:**

1. **Check Current Task** → `archon:manage_task(action="get", task_id="...")`
2. **Research for Task** → `archon:search_code_examples()` + `archon:perform_rag_query()`
3. **Implement the Task** → Write code based on research
4. **Update Task Status** → `archon:manage_task(action="update", task_id="...", update_fields={"status": "review"})`
5. **Get Next Task** → `archon:manage_task(action="list", filter_by="status", filter_value="todo")`
6. **Repeat Cycle**

**NEVER skip task updates with the Archon MCP server. NEVER code without checking current tasks first.**

## Project Scenarios & Initialization

### Scenario 1: New Project with Archon

```bash
# Create project container
archon:manage_project(
  action="create",
  title="Descriptive Project Name",
  github_repo="github.com/user/repo-name"
)

# Research → Plan → Create Tasks (see workflow below)
```

### Scenario 2: Existing Project - Adding Archon

```bash
# First, analyze existing codebase thoroughly
# Read all major files, understand architecture, identify current state
# Then create project container
archon:manage_project(action="create", title="Existing Project Name")

# Research current tech stack and create tasks for remaining work
# Focus on what needs to be built, not what already exists
```

### Scenario 3: Continuing Archon Project

```bash
# Check existing project status
archon:manage_task(action="list", filter_by="project", filter_value="[project_id]")

# Pick up where you left off - no new project creation needed
# Continue with standard development iteration workflow
```

### Universal Research & Planning Phase

**For all scenarios, research before task creation:**

```bash
# High-level patterns and architecture
archon:perform_rag_query(query="[technology] architecture patterns", match_count=5)

# Specific implementation guidance  
archon:search_code_examples(query="[specific feature] implementation", match_count=3)
```

**Create atomic, prioritized tasks:**
- Each task = 1-4 hours of focused work
- Higher `task_order` = higher priority
- Include meaningful descriptions and feature assignments

## Development Iteration Workflow

### Before Every Coding Session

**MANDATORY: Always check task status before writing any code:**

```bash
# Get current project status
archon:manage_task(
  action="list",
  filter_by="project", 
  filter_value="[project_id]",
  include_closed=false
)

# Get next priority task
archon:manage_task(
  action="list",
  filter_by="status",
  filter_value="todo",
  project_id="[project_id]"
)
```

### Task-Specific Research

**For each task, conduct focused research:**

```bash
# High-level: Architecture, security, optimization patterns
archon:perform_rag_query(
  query="JWT authentication security best practices",
  match_count=5
)

# Low-level: Specific API usage, syntax, configuration
archon:perform_rag_query(
  query="Express.js middleware setup validation",
  match_count=3
)

# Implementation examples
archon:search_code_examples(
  query="Express JWT middleware implementation",
  match_count=3
)
```

**Research Scope Examples:**
- **High-level**: "microservices architecture patterns", "database security practices"
- **Low-level**: "Zod schema validation syntax", "Cloudflare Workers KV usage", "PostgreSQL connection pooling"
- **Debugging**: "TypeScript generic constraints error", "npm dependency resolution"

### Task Execution Protocol

**1. Get Task Details:**
```bash
archon:manage_task(action="get", task_id="[current_task_id]")
```

**2. Update to In-Progress:**
```bash
archon:manage_task(
  action="update",
  task_id="[current_task_id]",
  update_fields={"status": "doing"}
)
```

**3. Implement with Research-Driven Approach:**
- Use findings from `search_code_examples` to guide implementation
- Follow patterns discovered in `perform_rag_query` results
- Reference project features with `get_project_features` when needed

**4. Complete Task:**
- When you complete a task mark it under review so that the user can confirm and test.
```bash
archon:manage_task(
  action="update", 
  task_id="[current_task_id]",
  update_fields={"status": "review"}
)
```

## Knowledge Management Integration

### Documentation Queries

**Use RAG for both high-level and specific technical guidance:**

```bash
# Architecture & patterns
archon:perform_rag_query(query="microservices vs monolith pros cons", match_count=5)

# Security considerations  
archon:perform_rag_query(query="OAuth 2.0 PKCE flow implementation", match_count=3)

# Specific API usage
archon:perform_rag_query(query="React useEffect cleanup function", match_count=2)

# Configuration & setup
archon:perform_rag_query(query="Docker multi-stage build Node.js", match_count=3)

# Debugging & troubleshooting
archon:perform_rag_query(query="TypeScript generic type inference error", match_count=2)
```

### Code Example Integration

**Search for implementation patterns before coding:**

```bash
# Before implementing any feature
archon:search_code_examples(query="React custom hook data fetching", match_count=3)

# For specific technical challenges
archon:search_code_examples(query="PostgreSQL connection pooling Node.js", match_count=2)
```

**Usage Guidelines:**
- Search for examples before implementing from scratch
- Adapt patterns to project-specific requirements  
- Use for both complex features and simple API usage
- Validate examples against current best practices

## Progress Tracking & Status Updates

### Daily Development Routine

**Start of each coding session:**

1. Check available sources: `archon:get_available_sources()`
2. Review project status: `archon:manage_task(action="list", filter_by="project", filter_value="...")`
3. Identify next priority task: Find highest `task_order` in "todo" status
4. Conduct task-specific research
5. Begin implementation

**End of each coding session:**

1. Update completed tasks to "done" status
2. Update in-progress tasks with current status
3. Create new tasks if scope becomes clearer
4. Document any architectural decisions or important findings

### Task Status Management

**Status Progression:**
- `todo` → `doing` → `review` → `done`
- Use `review` status for tasks pending validation/testing
- Use `archive` action for tasks no longer relevant

**Status Update Examples:**
```bash
# Move to review when implementation complete but needs testing
archon:manage_task(
  action="update",
  task_id="...",
  update_fields={"status": "review"}
)

# Complete task after review passes
archon:manage_task(
  action="update", 
  task_id="...",
  update_fields={"status": "done"}
)
```

## Research-Driven Development Standards

### Before Any Implementation

**Research checklist:**

- [ ] Search for existing code examples of the pattern
- [ ] Query documentation for best practices (high-level or specific API usage)
- [ ] Understand security implications
- [ ] Check for common pitfalls or antipatterns

### Knowledge Source Prioritization

**Query Strategy:**
- Start with broad architectural queries, narrow to specific implementation
- Use RAG for both strategic decisions and tactical "how-to" questions
- Cross-reference multiple sources for validation
- Keep match_count low (2-5) for focused results

## Project Feature Integration

### Feature-Based Organization

**Use features to organize related tasks:**

```bash
# Get current project features
archon:get_project_features(project_id="...")

# Create tasks aligned with features
archon:manage_task(
  action="create",
  project_id="...",
  title="...",
  feature="Authentication",  # Align with project features
  task_order=8
)
```

### Feature Development Workflow

1. **Feature Planning**: Create feature-specific tasks
2. **Feature Research**: Query for feature-specific patterns
3. **Feature Implementation**: Complete tasks in feature groups
4. **Feature Integration**: Test complete feature functionality

## Error Handling & Recovery

### When Research Yields No Results

**If knowledge queries return empty results:**

1. Broaden search terms and try again
2. Search for related concepts or technologies
3. Document the knowledge gap for future learning
4. Proceed with conservative, well-tested approaches

### When Tasks Become Unclear

**If task scope becomes uncertain:**

1. Break down into smaller, clearer subtasks
2. Research the specific unclear aspects
3. Update task descriptions with new understanding
4. Create parent-child task relationships if needed

### Project Scope Changes

**When requirements evolve:**

1. Create new tasks for additional scope
2. Update existing task priorities (`task_order`)
3. Archive tasks that are no longer relevant
4. Document scope changes in task descriptions

## Quality Assurance Integration

### Research Validation

**Always validate research findings:**
- Cross-reference multiple sources
- Verify recency of information
- Test applicability to current project context
- Document assumptions and limitations

### Task Completion Criteria

**Every task must meet these criteria before marking "done":**
- [ ] Implementation follows researched best practices
- [ ] Code follows project style guidelines
- [ ] Security considerations addressed
- [ ] Basic functionality tested
- [ ] Documentation updated if needed