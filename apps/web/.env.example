# Authentication
# Generate a secret with: openssl rand -base64 32
AUTH_SECRET=your-auth-secret-here

# Optional: Discord OAuth (remove if not using)
# AUTH_DISCORD_ID=
# AUTH_DISCORD_SECRET=

# Optional: Google OAuth (recommended)
# AUTH_GOOGLE_ID=
# AUTH_GOOGLE_SECRET=

# Database (optional for <PERSON>, using in-memory for now)
# DATABASE_URL=

# AWS Configuration (for Lambda integration)
AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=

# Bedrock Configuration
# BEDROCK_KNOWLEDGE_BASE_ID=

# API Gateway URL (set after deployment)
# NEXT_PUBLIC_API_GATEWAY_URL=https://your-api-gateway.execute-api.us-east-1.amazonaws.com

# Lambda Endpoint URL (for local testing with SAM)
# LAMBDA_ENDPOINT_URL=http://localhost:3001

# Node Environment
NODE_ENV=development

# Feature Flags for Development
NEXT_PUBLIC_USE_MOCK_DATA=true  # Set to false when <PERSON><PERSON> is configured
NEXT_PUBLIC_ENABLE_AWS=false    # Set to true when <PERSON>WS credentials are available