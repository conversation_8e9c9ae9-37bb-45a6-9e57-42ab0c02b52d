"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { BlurFade } from "~/components/ui/blur-fade";
import { toast } from "sonner";
import {
  FileText,
  Download,
  Copy,
  Eye,
  Plus,
  Clock,
  ChartBar,
  CheckCircle2,
  FileDown,
  FileJson,
  Sparkles,
  Calendar,
  TrendingUp,
  Users,
  Zap
} from "lucide-react";

export default function DashboardPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [studyCount, setStudyCount] = useState(0);

  // Animated counter for studies
  useEffect(() => {
    const target = store.completedStudies.length;
    const duration = 1000;
    const steps = 30;
    const increment = target / steps;
    let current = 0;
    
    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        setStudyCount(target);
        clearInterval(timer);
      } else {
        setStudyCount(Math.floor(current));
      }
    }, duration / steps);
    
    return () => clearInterval(timer);
  }, [store.completedStudies.length]);

  const handleCopyStudy = (studyId: string, synopsis: string) => {
    navigator.clipboard.writeText(synopsis);
    setCopiedId(studyId);
    toast.success("Synopsis copied to clipboard!");
    setTimeout(() => setCopiedId(null), 2000);
  };

  const handleExport = (format: "md" | "pdf" | "json", studyTitle: string) => {
    toast.info(`Exporting "${studyTitle}" as ${format.toUpperCase()}...`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSynopsisPreview = (synopsis: string) => {
    const lines = synopsis.split('\n').filter(line => line.trim());
    return lines.slice(0, 3).join(' ').substring(0, 200) + '...';
  };

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Animated gradient background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-50 via-white to-teal-50">
        <div className="absolute inset-0 bg-gradient-to-tr from-[#5A32FA]/5 via-transparent to-[#00C4CC]/5" />
      </div>
      
      {/* Floating orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-[#5A32FA]/20 to-[#7D2AE8]/20 blur-3xl animate-pulse" />
        <div className="absolute top-60 -right-40 h-96 w-96 rounded-full bg-gradient-to-br from-[#00C4CC]/20 to-[#5A32FA]/20 blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-40 h-72 w-72 rounded-full bg-gradient-to-br from-[#7D2AE8]/20 to-[#00C4CC]/20 blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Glass-morphic header */}
        <BlurFade delay={0} inView>
          <header className="sticky top-0 z-50 border-b border-white/20 bg-white/70 backdrop-blur-xl shadow-sm">
            <nav className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
              <div className="flex items-center gap-3">
                <Link href="/" className="flex items-center gap-3 group">
                  <div className="relative h-10 w-10 rounded-xl bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110">
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent" />
                    <Sparkles className="absolute inset-2 text-white" />
                  </div>
                  <span className="text-xl font-bold bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] bg-clip-text text-transparent">
                    TriaLynx Insights
                  </span>
                </Link>
              </div>
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[#5A32FA]/10 to-[#00C4CC]/10 border border-[#5A32FA]/20">
                  <TrendingUp className="h-4 w-4 text-[#5A32FA]" />
                  <span className="text-sm font-medium text-gray-700">
                    <span className="text-2xl font-bold text-[#5A32FA]">{studyCount}</span> Studies
                  </span>
                </div>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] flex items-center justify-center text-white font-semibold text-sm shadow-lg shadow-purple-500/25">
                  {store.completedStudies.length > 0 ? 'U' : 'G'}
                </div>
              </div>
            </nav>
          </header>
        </BlurFade>

        {/* Main Content */}
        <main className="mx-auto max-w-7xl px-6 py-12">
          <BlurFade delay={0.1} inView>
            <div className="mb-12">
              <h1 className="text-5xl font-bold bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] bg-clip-text text-transparent mb-3">
                Welcome to Your Dashboard
              </h1>
              <p className="text-lg text-gray-600">
                Design, manage, and analyze your clinical trials with AI-powered insights
              </p>
            </div>
          </BlurFade>

          {/* Quick Actions */}
          <div className="mb-12">
            <BlurFade delay={0.2} inView>
              <h2 className="mb-6 text-2xl font-bold text-gray-900">Quick Actions</h2>
            </BlurFade>
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <BlurFade delay={0.3} inView>
                <Link
                  href="/study/new"
                  className="group relative block overflow-hidden rounded-2xl border border-[#5A32FA]/20 bg-white/80 backdrop-blur-sm p-8 hover:border-[#5A32FA]/40 hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-300 hover:scale-[1.02]"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-[#5A32FA]/5 to-[#7D2AE8]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="relative">
                    <div className="mb-4 inline-flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-[#5A32FA] to-[#7D2AE8] shadow-lg shadow-purple-500/25 group-hover:shadow-xl group-hover:shadow-purple-500/30 transition-all duration-300 group-hover:scale-110">
                      <Plus className="h-7 w-7 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">New Study Design</h3>
                    <p className="text-sm text-gray-600 leading-relaxed mb-4">
                      Start designing a new clinical trial with AI-powered insights
                    </p>
                    <div className="flex items-center text-[#5A32FA] font-medium">
                      <span className="text-sm">Get Started</span>
                      <Zap className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </Link>
              </BlurFade>

              <BlurFade delay={0.4} inView>
                <div className="relative overflow-hidden rounded-2xl border border-gray-200/50 bg-white/50 backdrop-blur-sm p-8 opacity-70">
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-100/30 to-gray-200/30" />
                  <div className="relative">
                    <div className="mb-4 inline-flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-gray-400 to-gray-500 shadow-lg">
                      <FileText className="h-7 w-7 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-700 mb-2">Templates</h3>
                    <p className="text-sm text-gray-500 leading-relaxed">
                      Browse and use study design templates
                    </p>
                    <span className="absolute right-4 top-4 rounded-full bg-gradient-to-r from-gray-200 to-gray-300 px-3 py-1 text-xs font-medium text-gray-600">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </BlurFade>

              <BlurFade delay={0.5} inView>
                <div className="relative overflow-hidden rounded-2xl border border-gray-200/50 bg-white/50 backdrop-blur-sm p-8 opacity-70">
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-100/30 to-gray-200/30" />
                  <div className="relative">
                    <div className="mb-4 inline-flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-gray-400 to-gray-500 shadow-lg">
                      <ChartBar className="h-7 w-7 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-700 mb-2">Analytics</h3>
                    <p className="text-sm text-gray-500 leading-relaxed">
                      View insights and trends from your designs
                    </p>
                    <span className="absolute right-4 top-4 rounded-full bg-gradient-to-r from-gray-200 to-gray-300 px-3 py-1 text-xs font-medium text-gray-600">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </BlurFade>
            </div>
          </div>

          {/* Completed Studies */}
          <div>
            <BlurFade delay={0.6} inView>
              <h2 className="mb-6 text-2xl font-bold text-gray-900">
                {store.completedStudies.length > 0 ? 'Completed Studies' : 'Recent Activity'}
              </h2>
            </BlurFade>
            
            {store.completedStudies.length === 0 ? (
              <BlurFade delay={0.7} inView>
                <div className="rounded-2xl border border-gray-200/50 bg-white/80 backdrop-blur-sm p-12 text-center">
                  <div className="inline-flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-gray-100 to-gray-200 mb-6">
                    <Clock className="h-10 w-10 text-gray-400" />
                  </div>
                  <p className="text-xl text-gray-600 font-medium mb-2">No completed studies yet</p>
                  <p className="text-gray-500 mb-6">
                    Start by creating your first clinical trial design
                  </p>
                  <Link
                    href="/study/new"
                    className="inline-flex items-center rounded-xl bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] px-6 py-3 text-sm font-medium text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transition-all duration-300 hover:scale-105"
                  >
                    Create New Design
                    <Plus className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </BlurFade>
            ) : (
              <div className="space-y-6">
                {store.completedStudies.map((study, index) => (
                  <BlurFade key={study.id} delay={0.7 + index * 0.1} inView>
                    <Card className="group overflow-hidden border-[#5A32FA]/10 bg-white/80 backdrop-blur-sm hover:border-[#5A32FA]/30 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300">
                      <div className="absolute inset-0 bg-gradient-to-br from-[#5A32FA]/5 to-[#00C4CC]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <CardHeader className="relative pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-[#5A32FA] transition-colors">
                              {study.title}
                            </CardTitle>
                            <CardDescription className="mt-2 flex items-center gap-3">
                              <span className="flex items-center gap-1.5 text-gray-500">
                                <Calendar className="h-3.5 w-3.5" />
                                {formatDate(study.completedAt)}
                              </span>
                            </CardDescription>
                          </div>
                          {study.phase && (
                            <Badge className="ml-4 bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8] text-white border-0 px-3 py-1 shadow-lg shadow-purple-500/25">
                              {study.phase.toUpperCase()}
                            </Badge>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="relative">
                        <div className="relative overflow-hidden">
                          <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                            {getSynopsisPreview(study.synopsis)}
                          </p>
                          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white/80 to-transparent pointer-events-none" />
                        </div>
                        <div className="flex gap-2 mt-4">
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-[#5A32FA]/20 hover:border-[#5A32FA]/40 hover:bg-[#5A32FA]/5 transition-all"
                            onClick={() => {
                              store.setSynopsis(study.synopsis);
                              store.updateDiscovery(study.discovery);
                              router.push('/study/new/review');
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-[#5A32FA]/20 hover:border-[#5A32FA]/40 hover:bg-[#5A32FA]/5 transition-all"
                            onClick={() => handleCopyStudy(study.id, study.synopsis)}
                          >
                            {copiedId === study.id ? (
                              <>
                                <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                                Copied!
                              </>
                            ) : (
                              <>
                                <Copy className="mr-2 h-4 w-4" />
                                Copy
                              </>
                            )}
                          </Button>
                          <div className="flex gap-1 ml-auto">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="hover:bg-[#5A32FA]/5"
                              onClick={() => handleExport("md", study.title)}
                            >
                              <FileDown className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="hover:bg-[#5A32FA]/5"
                              onClick={() => handleExport("pdf", study.title)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="hover:bg-[#5A32FA]/5"
                              onClick={() => handleExport("json", study.title)}
                            >
                              <FileJson className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </BlurFade>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}