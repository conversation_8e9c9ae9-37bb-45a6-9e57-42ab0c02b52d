"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { SourceStudiesViewerPortal } from "~/components/insights/SourceStudiesViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Building2, 
  Users, 
  TrendingUp,
  Database,
  Monitor,
  Globe,
  Plus,
  X
} from "lucide-react";

export default function OperationalPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    numberOfSites: store.discovery.operational?.numberOfSites || "5",
    sitesPerCountry: store.discovery.operational?.sitesPerCountry || {} as Record<string, number>,
    recruitmentRate: store.discovery.operational?.recruitmentRate || "2-3 patients/site/month",
    screenFailureRate: store.discovery.operational?.screenFailureRate || "20%",
    dropoutRate: store.discovery.operational?.dropoutRate || "15%",
    dataManagement: store.discovery.operational?.dataManagement || "edc" as "edc" | "paper" | "hybrid",
    monitoringApproach: store.discovery.operational?.monitoringApproach || "risk-based" as "on-site" | "remote" | "risk-based" | "hybrid",
  });

  const [newCountry, setNewCountry] = useState("");
  const [newSiteCount, setNewSiteCount] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  const [sourceStudiesViewerOpen, setSourceStudiesViewerOpen] = useState(false);
  const [sourceStudiesViewerSources, setSourceStudiesViewerSources] = useState<any[]>([]);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined, // Clear progress status when real data arrives
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
      // Clear progress status on error
      if (error && typeof error === 'object' && 'data' in error) {
        const errorData = error as any;
        const field = errorData.data?.field;
        if (field) {
          setInsightsData(prev => ({
            ...prev,
            [field]: {
              sections: [],
              sources: [],
              progressStatus: undefined,
              progressMessages: [],
            }
          }));
        }
      }
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("operational");
      router.push("/study/new/review");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    // Check cache first unless forcing refresh
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    // Show panel immediately with loading state
    setActiveInsightsPanel(field);
    
    // Set initial progress status
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Initializing search...',
        progressMessages: [],
      }
    }));
    
    // Progressive status updates (10-12 second timeline)
    const progressUpdates = field === 'site-planning' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching knowledge base...' },
      { delay: 3500, message: 'Analyzing site strategies...' },
      { delay: 6000, message: 'Extracting recruitment patterns...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'enrollment-projections' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching knowledge base...' },
      { delay: 3500, message: 'Analyzing enrollment data...' },
      { delay: 6000, message: 'Calculating projections...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching knowledge base...' },
      { delay: 3500, message: 'Analyzing monitoring approaches...' },
      { delay: 6000, message: 'Evaluating data strategies...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    // Update progress status progressively
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          // Only update if we're still loading (haven't received results yet)
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      targetEnrollment: store.discovery.population?.targetEnrollment || undefined,
      geographicScope: store.discovery.population?.geographicScope || undefined,
    };

    const queries: Record<string, string> = {
      "site-planning": `What are typical site requirements and recruitment rates for ${store.discovery.condition || "clinical"} trials?`,
      "enrollment-projections": `What are typical enrollment metrics and dropout rates for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
      "data-monitoring": `What data management and monitoring approaches are used in ${store.discovery.phase || "Phase 2/3"} trials?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  const addCountry = () => {
    if (newCountry && newSiteCount) {
      const count = parseInt(newSiteCount);
      if (!isNaN(count) && count > 0) {
        setFormData(prev => ({
          ...prev,
          sitesPerCountry: {
            ...prev.sitesPerCountry,
            [newCountry]: count
          }
        }));
        setNewCountry("");
        setNewSiteCount("");
      }
    }
  };

  const removeCountry = (country: string) => {
    setFormData(prev => {
      const updated = { ...prev.sitesPerCountry };
      delete updated[country];
      return { ...prev, sitesPerCountry: updated };
    });
  };

  const getTotalSites = () => {
    return Object.values(formData.sitesPerCountry).reduce((sum, count) => sum + count, 0);
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    if (!formData.numberOfSites) {
      toast.error("Please specify the number of sites");
      return;
    }

    store.updateDiscovery({ 
      operational: formData
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        operational: formData,
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    if (field === "site-planning") {
      // Use actionableData from JSON response if available
      if (actionableData) {
        const updates: any = {};
        
        if (actionableData.field === 'numberOfSites') {
          updates.numberOfSites = actionableData.value;
          toast.success("Number of sites updated");
        } else if (actionableData.field === 'recruitmentRate') {
          updates.recruitmentRate = actionableData.value;
          toast.success("Recruitment rate updated");
        } else if (actionableData.field === 'sitesPerCountry' && actionableData.value) {
          updates.sitesPerCountry = actionableData.value;
          toast.success("Geographic distribution updated");
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      } else {
        // Fallback to regex parsing
        const sitesMatch = suggestion.match(/(\d+)[\s-]+(\d+)?\s*sites?/i);
        const recruitmentMatch = suggestion.match(/(\d+)[\s-]+(\d+)?\s*patients?.*per\s*(?:site|month)/i);
        
        if (sitesMatch) {
          const sites = sitesMatch[2] ? `${sitesMatch[1]}-${sitesMatch[2]}` : sitesMatch[1];
          setFormData(prev => ({ ...prev, numberOfSites: sites }));
        }
        if (recruitmentMatch) {
          const rate = recruitmentMatch[2] 
            ? `${recruitmentMatch[1]}-${recruitmentMatch[2]} patients/site/month`
            : `${recruitmentMatch[1]} patients/site/month`;
          setFormData(prev => ({ ...prev, recruitmentRate: rate }));
        }
        
        toast.success("Site planning parameters updated");
      }
      return;
    }
    
    if (field === "enrollment-projections") {
      // Use actionableData from JSON response if available
      if (actionableData) {
        const updates: any = {};
        
        if (actionableData.field === 'screenFailureRate') {
          updates.screenFailureRate = actionableData.value;
          toast.success("Screen failure rate updated");
        } else if (actionableData.field === 'dropoutRate') {
          updates.dropoutRate = actionableData.value;
          toast.success("Dropout rate updated");
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "data-monitoring") {
      // Use actionableData from JSON response if available
      if (actionableData) {
        const updates: any = {};
        
        if (actionableData.field === 'dataManagement') {
          updates.dataManagement = actionableData.value;
          toast.success("Data management system updated");
        } else if (actionableData.field === 'monitoringApproach') {
          updates.monitoringApproach = actionableData.value;
          toast.success("Monitoring approach updated");
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
  };

  const handleViewAllSources = () => {
    if (activeInsightsPanel && (insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources)) {
      const sources = insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || [];
      setSourceStudiesViewerSources(sources);
      setSourceStudiesViewerOpen(true);
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Operational Planning
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Plan site selection, recruitment, and data management
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Site Planning
                </CardTitle>
                <CardDescription>
                  Define site requirements and geographic distribution
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("site-planning")}
                onRefresh={() => handleGetInsights("site-planning", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "site-planning"}
                hasCachedData={!!cachedInsights["site-planning"]}
                showRefresh={!!cachedInsights["site-planning"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="numberOfSites">Total Number of Sites</Label>
                <Input
                  id="numberOfSites"
                  placeholder="e.g., 5, 10-15"
                  value={formData.numberOfSites}
                  onChange={(e) => setFormData({ ...formData, numberOfSites: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="recruitmentRate">Expected Recruitment Rate</Label>
                <Input
                  id="recruitmentRate"
                  placeholder="e.g., 2-3 patients/site/month"
                  value={formData.recruitmentRate}
                  onChange={(e) => setFormData({ ...formData, recruitmentRate: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label>Sites by Country/Region</Label>
              {Object.entries(formData.sitesPerCountry).map(([country, count]) => (
                <div key={country} className="flex items-center gap-2 rounded-lg border p-3">
                  <Globe className="h-4 w-4 text-gray-400" />
                  <span className="flex-1 text-sm">{country}</span>
                  <Badge variant="secondary">{count} sites</Badge>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeCountry(country)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Country/Region name"
                  value={newCountry}
                  onChange={(e) => setNewCountry(e.target.value)}
                />
                <Input
                  placeholder="# of sites"
                  type="number"
                  className="w-32"
                  value={newSiteCount}
                  onChange={(e) => setNewSiteCount(e.target.value)}
                />
                <Button onClick={addCountry} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {getTotalSites() > 0 && (
                <div className="rounded-lg bg-purple-50 dark:bg-purple-900/20 p-3">
                  <p className="text-sm font-medium text-purple-900 dark:text-purple-200">
                    Total: {getTotalSites()} sites across {Object.keys(formData.sitesPerCountry).length} countries
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Enrollment Projections
                </CardTitle>
                <CardDescription>
                  Expected attrition and enrollment rates
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("enrollment-projections")}
                onRefresh={() => handleGetInsights("enrollment-projections", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "enrollment-projections"}
                hasCachedData={!!cachedInsights["enrollment-projections"]}
                showRefresh={!!cachedInsights["enrollment-projections"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="screenFailureRate">Screen Failure Rate</Label>
                <Input
                  id="screenFailureRate"
                  placeholder="e.g., 20%, 15-25%"
                  value={formData.screenFailureRate}
                  onChange={(e) => setFormData({ ...formData, screenFailureRate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dropoutRate">Expected Dropout Rate</Label>
                <Input
                  id="dropoutRate"
                  placeholder="e.g., 15%, 10-20%"
                  value={formData.dropoutRate}
                  onChange={(e) => setFormData({ ...formData, dropoutRate: e.target.value })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Data Management & Monitoring
                </CardTitle>
                <CardDescription>
                  Define data collection and monitoring strategies
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("data-monitoring")}
                onRefresh={() => handleGetInsights("data-monitoring", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "data-monitoring"}
                hasCachedData={!!cachedInsights["data-monitoring"]}
                showRefresh={!!cachedInsights["data-monitoring"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dataManagement">Data Management System</Label>
                <Select
                  value={formData.dataManagement}
                  onValueChange={(value) => setFormData({ ...formData, dataManagement: value as any })}
                >
                  <SelectTrigger id="dataManagement">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="edc">Electronic Data Capture (EDC)</SelectItem>
                    <SelectItem value="paper">Paper CRF</SelectItem>
                    <SelectItem value="hybrid">Hybrid (EDC + Paper)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="monitoringApproach">Monitoring Approach</Label>
                <Select
                  value={formData.monitoringApproach}
                  onValueChange={(value) => setFormData({ ...formData, monitoringApproach: value as any })}
                >
                  <SelectTrigger id="monitoringApproach">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="on-site">100% On-site Monitoring</SelectItem>
                    <SelectItem value="remote">Remote Monitoring</SelectItem>
                    <SelectItem value="risk-based">Risk-Based Monitoring</SelectItem>
                    <SelectItem value="hybrid">Hybrid Monitoring</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.5} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/timeline")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Review"}
          </Button>
        </div>
      </BlurFade>

      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onViewAllSources={handleViewAllSources}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      <DocumentViewerPortal
        citations={(() => {
          // Extract all unique citations from all sections
          const allCitations = new Map<string, any>();
          Object.values(insightsData).forEach(data => {
            data.sections?.forEach((section: any) => {
              if (section.citations && Array.isArray(section.citations)) {
                section.citations.forEach((citation: any) => {
                  if (citation.id && !allCitations.has(citation.id)) {
                    allCitations.set(citation.id, citation);
                  }
                });
              }
            });
          });
          return Array.from(allCitations.values());
        })()}
        sources={(() => {
          // Extract all unique sources from all insights data
          const allSources = new Map<string, any>();
          Object.values(insightsData).forEach(data => {
            data.sources?.forEach((source: any) => {
              if (source.nctId && !allSources.has(source.nctId)) {
                allSources.set(source.nctId, source);
              }
            });
          });
          return Array.from(allSources.values());
        })()}
        onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
        currentStudyId={(() => {
          // Extract NCT ID from the current document URL
          if (!documentViewerUrl) return undefined;
          const match = documentViewerUrl.match(/NCT\\d+/i);
          return match ? match[0] : undefined;
        })()}
        isOpen={!!documentViewerUrl}
        onClose={() => setDocumentViewerUrl(null)}
        documentUrl={documentViewerUrl}
        loading={false}
      />
      
      <SourceStudiesViewerPortal
        isOpen={sourceStudiesViewerOpen}
        onClose={() => setSourceStudiesViewerOpen(false)}
        sources={sourceStudiesViewerSources}
        onDocumentClick={(url) => setDocumentViewerUrl(url)}
      />
    </div>
  );
}