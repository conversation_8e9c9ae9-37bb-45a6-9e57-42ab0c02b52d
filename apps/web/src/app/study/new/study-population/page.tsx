"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Users,
  MapPin,
  Globe,
  UserCheck,
  UserX,
  Settings,
  Building2,
  Plus,
  X
} from "lucide-react";

export default function StudyPopulationPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    // Basic Demographics (existing)
    targetEnrollment: store.discovery.population.targetEnrollment || "",
    ageMin: store.discovery.population.ageMin || 18,
    ageMax: store.discovery.population.ageMax || 65,
    gender: store.discovery.population.gender || "all",
    specificPopulation: store.discovery.population.specificPopulation || "",
    healthyVolunteers: store.discovery.population.healthyVolunteers ?? false,
    geographicScope: store.discovery.population.geographicScope || "national",
    
    // Inclusion/Exclusion Criteria (existing)
    inclusionCriteria: store.discovery.population.inclusionCriteria || [],
    exclusionCriteria: store.discovery.population.exclusionCriteria || [],
    
    // Enhanced Population Details (new critical questions)
    trialAssignmentMethod: store.discovery.population.trialAssignmentMethod || "randomization",
    numberOfSites: store.discovery.population.numberOfSites || "",
    siteDistribution: store.discovery.population.siteDistribution || "",
    countriesEngaged: store.discovery.population.countriesEngaged || [],
    sitesPerCountry: store.discovery.population.sitesPerCountry || {},
  });

  const [newInclusionCriteria, setNewInclusionCriteria] = useState("");
  const [newExclusionCriteria, setNewExclusionCriteria] = useState("");
  const [newCountry, setNewCountry] = useState("");
  const [newSiteCount, setNewSiteCount] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined,
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("study-population");
      router.push("/study/new/safety-assessment");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    setActiveInsightsPanel(field);
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Analyzing population strategies...',
        progressMessages: [],
      }
    }));
    
    const progressUpdates = field === 'inclusion-exclusion' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching study populations...' },
      { delay: 3500, message: 'Analyzing inclusion criteria...' },
      { delay: 6000, message: 'Extracting exclusion patterns...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'exclusion-criteria' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching safety criteria...' },
      { delay: 3500, message: 'Analyzing exclusion patterns...' },
      { delay: 6000, message: 'Extracting safety considerations...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'site-strategy' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching site strategies...' },
      { delay: 3500, message: 'Analyzing geographic distribution...' },
      { delay: 6000, message: 'Extracting recruitment patterns...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching enrollment data...' },
      { delay: 3500, message: 'Analyzing target populations...' },
      { delay: 6000, message: 'Extracting demographics...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      drugName: store.discovery.intervention.name || undefined,
      primaryEndpoint: store.discovery.objectives?.primaryGoal || undefined,
      designType: store.discovery.design?.designType || undefined,
      targetEnrollment: formData.targetEnrollment || undefined,
      currentAgeMin: formData.ageMin || undefined,
      currentAgeMax: formData.ageMax || undefined,
      currentGender: formData.gender || undefined,
      currentHealthyVolunteers: formData.healthyVolunteers || undefined,
      geographicScope: formData.geographicScope || undefined,
      currentAssignmentMethod: formData.trialAssignmentMethod || undefined,
      drugClass: store.discovery.intervention.drugClass || undefined,
      currentInclusionCriteria: formData.inclusionCriteria || [],
      currentExclusionCriteria: formData.exclusionCriteria || [],
    };

    const queries: Record<string, string> = {
      "demographics": `What are typical demographics and enrollment targets for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
      "inclusion-exclusion": `What are common inclusion and exclusion criteria for ${store.discovery.condition || "clinical"} trials?`,
      "exclusion-criteria": `What are critical exclusion criteria and safety considerations for ${store.discovery.condition || "clinical"} trials?`,
      "site-strategy": `What are typical site strategies and geographic distribution for ${store.discovery.condition || "clinical"} trials?`,
      "assignment-method": `What are optimal trial assignment methods and randomization strategies for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  // Array management functions
  const addInclusionCriteria = () => {
    if (newInclusionCriteria.trim()) {
      setFormData(prev => ({
        ...prev,
        inclusionCriteria: [...prev.inclusionCriteria, newInclusionCriteria.trim()]
      }));
      setNewInclusionCriteria("");
    }
  };

  const removeInclusionCriteria = (index: number) => {
    setFormData(prev => ({
      ...prev,
      inclusionCriteria: prev.inclusionCriteria.filter((_, i) => i !== index)
    }));
  };

  const addExclusionCriteria = () => {
    if (newExclusionCriteria.trim()) {
      setFormData(prev => ({
        ...prev,
        exclusionCriteria: [...prev.exclusionCriteria, newExclusionCriteria.trim()]
      }));
      setNewExclusionCriteria("");
    }
  };

  const removeExclusionCriteria = (index: number) => {
    setFormData(prev => ({
      ...prev,
      exclusionCriteria: prev.exclusionCriteria.filter((_, i) => i !== index)
    }));
  };

  const addCountry = () => {
    if (newCountry.trim() && newSiteCount.trim()) {
      const count = parseInt(newSiteCount);
      if (!isNaN(count) && count > 0) {
        // Add to countries list if not already there
        if (!formData.countriesEngaged.includes(newCountry.trim())) {
          setFormData(prev => ({
            ...prev,
            countriesEngaged: [...prev.countriesEngaged, newCountry.trim()],
            sitesPerCountry: {
              ...prev.sitesPerCountry,
              [newCountry.trim()]: count
            }
          }));
        } else {
          // Update existing country's site count
          setFormData(prev => ({
            ...prev,
            sitesPerCountry: {
              ...prev.sitesPerCountry,
              [newCountry.trim()]: count
            }
          }));
        }
        setNewCountry("");
        setNewSiteCount("");
      }
    } else if (newCountry.trim()) {
      // Add country without site count
      if (!formData.countriesEngaged.includes(newCountry.trim())) {
        setFormData(prev => ({
          ...prev,
          countriesEngaged: [...prev.countriesEngaged, newCountry.trim()]
        }));
        setNewCountry("");
      }
    }
  };

  const removeCountry = (country: string) => {
    setFormData(prev => {
      const updatedSitesPerCountry = { ...prev.sitesPerCountry };
      delete updatedSitesPerCountry[country];
      
      return {
        ...prev,
        countriesEngaged: prev.countriesEngaged.filter(c => c !== country),
        sitesPerCountry: updatedSitesPerCountry
      };
    });
  };

  const getTotalSites = () => {
    return Object.values(formData.sitesPerCountry).reduce((sum, count) => sum + count, 0);
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Validation
    if (!formData.targetEnrollment) {
      toast.error("Please specify the target enrollment");
      return;
    }
    
    if (formData.inclusionCriteria.length === 0) {
      toast.error("Please add at least one inclusion criterion");
      return;
    }

    if (formData.exclusionCriteria.length === 0) {
      toast.error("Please add at least one exclusion criterion");
      return;
    }

    // Update store with comprehensive population data
    store.updateDiscovery({ 
      population: {
        // Basic demographics
        targetEnrollment: formData.targetEnrollment,
        ageMin: formData.ageMin,
        ageMax: formData.ageMax,
        gender: formData.gender as any,
        specificPopulation: formData.specificPopulation,
        healthyVolunteers: formData.healthyVolunteers,
        geographicScope: formData.geographicScope as any,
        
        // Criteria
        inclusionCriteria: formData.inclusionCriteria,
        exclusionCriteria: formData.exclusionCriteria,
        
        // Enhanced details (critical questions)
        trialAssignmentMethod: formData.trialAssignmentMethod,
        numberOfSites: formData.numberOfSites,
        siteDistribution: formData.siteDistribution,
        countriesEngaged: formData.countriesEngaged,
        sitesPerCountry: formData.sitesPerCountry,
      }
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        population: {
          targetEnrollment: formData.targetEnrollment,
          ageMin: formData.ageMin,
          ageMax: formData.ageMax,
          gender: formData.gender,
          specificPopulation: formData.specificPopulation,
          healthyVolunteers: formData.healthyVolunteers,
          geographicScope: formData.geographicScope,
          inclusionCriteria: formData.inclusionCriteria,
          exclusionCriteria: formData.exclusionCriteria,
          trialAssignmentMethod: formData.trialAssignmentMethod,
          numberOfSites: formData.numberOfSites,
          siteDistribution: formData.siteDistribution,
          countriesEngaged: formData.countriesEngaged,
          sitesPerCountry: formData.sitesPerCountry,
        }
      },
    });
  };

  // Helper function to parse site distribution content into components
  const parseSiteDistributionContent = (content: string) => {
    if (!content?.trim()) {
      return { strategyText: '', bulletPoints: [] };
    }

    const lines = content.split('\n');
    const strategyLines: string[] = [];
    const bulletPoints: string[] = [];
    let inBulletSection = false;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Skip section headers
      if (trimmedLine.includes('---') && 
          (trimmedLine.includes('Priority') || trimmedLine.includes('Recommendations') || trimmedLine.includes('Strategy'))) {
        inBulletSection = trimmedLine.includes('Priority') || trimmedLine.includes('Recommendations');
        continue;
      }
      
      // Detect bullet points
      if (trimmedLine.startsWith('•')) {
        bulletPoints.push(trimmedLine);
        inBulletSection = true;
      } else if (trimmedLine && !inBulletSection) {
        // Strategy text (non-bullet, non-empty lines before bullet section)
        strategyLines.push(line);
      }
    }

    return {
      strategyText: strategyLines.join('\n').trim(),
      bulletPoints: bulletPoints
    };
  };

  // Helper function to format site distribution content in correct order
  const formatSiteDistributionContent = (strategyText: string, bulletPoints: string[]) => {
    const parts: string[] = [];
    
    // Add strategy text at top if present
    if (strategyText?.trim()) {
      parts.push(strategyText.trim());
    }
    
    // Add bullet points section if present
    if (bulletPoints.length > 0) {
      if (parts.length > 0) {
        parts.push(''); // Empty line separator
      }
      parts.push(...bulletPoints);
    }
    
    return parts.join('\n');
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    if (field === "demographics") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'targetEnrollment') {
          updates.targetEnrollment = actionableData.value;
          toast.success("Target enrollment updated");
        }
        if (actionableData.field === 'ageRange') {
          updates.ageMin = actionableData.min;
          updates.ageMax = actionableData.max;
          toast.success("Age range updated");
        }
        if (actionableData.field === 'gender') {
          updates.gender = actionableData.value;
          toast.success("Gender preference updated");
        }
        if (actionableData.field === 'healthyVolunteers') {
          updates.healthyVolunteers = actionableData.value;
          toast.success("Healthy volunteers setting updated");
        }
        if (actionableData.field === 'specificPopulation') {
          updates.specificPopulation = actionableData.value;
          toast.success("Population characteristics updated");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "inclusion-exclusion") {
      if (actionableData) {
        const updates: any = {};
        // Handle legacy field names
        if (actionableData.field === 'inclusionCriteria' && actionableData.criteria) {
          updates.inclusionCriteria = [...formData.inclusionCriteria, ...actionableData.criteria];
          toast.success("Inclusion criteria added");
        }
        // Handle new bulk field names from Lambda
        if (actionableData.field === 'bulkInclusionCriteria' && actionableData.criteria) {
          updates.inclusionCriteria = [...formData.inclusionCriteria, ...actionableData.criteria];
          const category = actionableData.category || 'general';
          toast.success(`${category.charAt(0).toUpperCase() + category.slice(1)} inclusion criteria added`);
        }
        // Handle individual single criterion
        if (actionableData.field === 'singleInclusionCriterion' && actionableData.value) {
          // Check if criterion already exists to avoid duplicates
          if (!formData.inclusionCriteria.includes(actionableData.value)) {
            updates.inclusionCriteria = [...formData.inclusionCriteria, actionableData.value];
            const category = actionableData.category || 'general';
            toast.success(`${category.charAt(0).toUpperCase() + category.slice(1)} criterion added`);
          } else {
            toast.info("This criterion is already in your inclusion list");
          }
        }
        if (actionableData.field === 'exclusionCriteria' && actionableData.criteria) {
          updates.exclusionCriteria = [...formData.exclusionCriteria, ...actionableData.criteria];
          toast.success("Exclusion criteria added");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "assignment-method") {
      if (actionableData) {
        const updates: any = {};
        if (actionableData.field === 'trialAssignmentMethod') {
          updates.trialAssignmentMethod = actionableData.value;
          toast.success("Assignment method updated");
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
    
    if (field === "exclusion-criteria") {
      if (actionableData) {
        const updates: any = {};
        // Handle bulk exclusion criteria
        if (actionableData.field === 'bulkExclusionCriteria' && actionableData.criteria) {
          updates.exclusionCriteria = [...formData.exclusionCriteria, ...actionableData.criteria];
          const category = actionableData.category || 'general';
          toast.success(`${category.charAt(0).toUpperCase() + category.slice(1)} exclusion criteria added`);
        }
        // Handle individual single exclusion criterion
        if (actionableData.field === 'singleExclusionCriterion' && actionableData.value) {
          // Check if criterion already exists to avoid duplicates
          if (!formData.exclusionCriteria.includes(actionableData.value)) {
            updates.exclusionCriteria = [...formData.exclusionCriteria, actionableData.value];
            const category = actionableData.category || 'general';
            toast.success(`${category.charAt(0).toUpperCase() + category.slice(1)} exclusion criterion added`);
          } else {
            toast.info("This criterion is already in your exclusion list");
          }
        }
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }

    if (field === "site-strategy") {
      if (actionableData) {
        const updates: any = {};
        
        // Legacy field support (numberOfSites)
        if (actionableData.field === 'numberOfSites') {
          updates.numberOfSites = actionableData.value;
          toast.success("Number of sites updated");
        }
        
        // Legacy field support (countriesEngaged)
        if (actionableData.field === 'countriesEngaged' && actionableData.countries) {
          updates.countriesEngaged = [...new Set([...formData.countriesEngaged, ...actionableData.countries])];
          toast.success("Countries updated");
        }
        
        // New comprehensive site strategy fields
        if (actionableData.field === 'siteCountStrategy') {
          if (actionableData.numberOfSites) {
            updates.numberOfSites = actionableData.numberOfSites;
          }
          if (actionableData.siteDistribution) {
            const currentContent = formData.siteDistribution || '';
            const newStrategy = actionableData.siteDistribution;
            
            // Parse existing content
            const { strategyText: existingStrategy, bulletPoints } = parseSiteDistributionContent(currentContent);
            
            // Check if this strategy is already present to avoid duplicates
            if (!existingStrategy.includes(newStrategy)) {
              // Update strategy text (always goes to top), preserve bullet points
              const updatedContent = formatSiteDistributionContent(newStrategy, bulletPoints);
              updates.siteDistribution = updatedContent;
              toast.success("Site distribution strategy added to top");
            } else {
              toast.info("This distribution strategy is already present");
            }
          }
        }
        
        // Bulk countries with site counts
        if (actionableData.field === 'bulkCountries') {
          if (actionableData.countries && actionableData.countries.length > 0) {
            updates.countriesEngaged = [...new Set([...formData.countriesEngaged, ...actionableData.countries])];
          }
          if (actionableData.sitesPerCountry) {
            updates.sitesPerCountry = { ...formData.sitesPerCountry, ...actionableData.sitesPerCountry };
          }
          toast.success("Countries and site counts updated");
        }
        
        // Individual country recommendation
        if (actionableData.field === 'singleCountryRecommendation') {
          const { country, sites, rationale } = actionableData;
          if (country) {
            // Check if country already exists to avoid duplicates
            if (!formData.countriesEngaged.includes(country)) {
              updates.countriesEngaged = [...formData.countriesEngaged, country];
              if (sites && !isNaN(parseInt(sites))) {
                updates.sitesPerCountry = { ...formData.sitesPerCountry, [country]: parseInt(sites) };
              }
              const category = actionableData.category || 'geographic';
              toast.success(`${category.charAt(0).toUpperCase() + category.slice(1)} country added: ${country}`);
            } else {
              // Update site count if country exists
              if (sites && !isNaN(parseInt(sites))) {
                updates.sitesPerCountry = { ...formData.sitesPerCountry, [country]: parseInt(sites) };
                toast.success(`Site count updated for ${country}: ${sites} sites`);
              } else {
                toast.info(`${country} is already in your countries list`);
              }
            }
          }
        }
        
        // Individual site recommendation/strategy
        if (actionableData.field === 'singleSiteRecommendation' && actionableData.value) {
          const recommendation = actionableData.value;
          const category = actionableData.category || 'strategic';
          const categoryLabel = category.charAt(0).toUpperCase() + category.slice(1);
          const newRecommendation = `• ${categoryLabel}: ${recommendation}`;
          
          // Parse existing content
          const currentContent = formData.siteDistribution || '';
          const { strategyText, bulletPoints } = parseSiteDistributionContent(currentContent);
          
          // Check if this recommendation is already present to avoid duplicates
          if (!bulletPoints.some(bullet => bullet.includes(recommendation))) {
            // Add new bullet point, maintaining strategy at top
            const updatedBulletPoints = [...bulletPoints, newRecommendation];
            const updatedContent = formatSiteDistributionContent(strategyText, updatedBulletPoints);
            
            updates.siteDistribution = updatedContent;
            toast.success(`${categoryLabel} recommendation added to priority list`);
          } else {
            toast.info(`This recommendation is already in your priority list`);
          }
        }
        
        // Bulk site strategy application
        if (actionableData.field === 'bulkSiteStrategy' && actionableData.recommendations) {
          const recommendations = actionableData.recommendations;
          const category = actionableData.category || 'strategic';
          const categoryLabel = category.charAt(0).toUpperCase() + category.slice(1);
          
          // Parse existing content
          const currentContent = formData.siteDistribution || '';
          const { strategyText, bulletPoints } = parseSiteDistributionContent(currentContent);
          
          // Format new recommendations as bullet points
          const newBulletPoints = recommendations
            .filter((rec: string) => !bulletPoints.some(bullet => bullet.includes(rec)))
            .map((rec: string) => `• ${categoryLabel}: ${rec}`);
          
          if (newBulletPoints.length > 0) {
            // Add new bullet points, maintaining strategy at top
            const updatedBulletPoints = [...bulletPoints, ...newBulletPoints];
            const updatedContent = formatSiteDistributionContent(strategyText, updatedBulletPoints);
            
            updates.siteDistribution = updatedContent;
            toast.success(`${newBulletPoints.length} priority recommendations added`);
          } else {
            toast.info(`All recommendations are already in your priority list`);
          }
        }
        
        // Site distribution strategy field
        if (actionableData.field === 'siteDistribution' && actionableData.value) {
          updates.siteDistribution = actionableData.value;
          toast.success("Site distribution strategy updated");
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
        }
      }
      return;
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Study Population
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Define your target patient population and enrollment strategy
          </p>
        </div>
      </BlurFade>

      {/* Target Demographics */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Target Demographics
                </CardTitle>
                <CardDescription>
                  Define the basic demographic characteristics of your study population
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("demographics")}
                onRefresh={() => handleGetInsights("demographics", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "demographics"}
                hasCachedData={!!cachedInsights["demographics"]}
                showRefresh={!!cachedInsights["demographics"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="targetEnrollment">Total Number of Participants *</Label>
                <Input
                  id="targetEnrollment"
                  placeholder="e.g., 300, 150-200"
                  value={formData.targetEnrollment}
                  onChange={(e) => setFormData({ ...formData, targetEnrollment: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="gender">Gender</Label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) => setFormData({ ...formData, gender: value as any })}
                >
                  <SelectTrigger id="gender">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="male">Male only</SelectItem>
                    <SelectItem value="female">Female only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ageMin">Minimum Age</Label>
                <Input
                  id="ageMin"
                  type="number"
                  min="0"
                  max="120"
                  value={formData.ageMin}
                  onChange={(e) => setFormData({ ...formData, ageMin: parseInt(e.target.value) || 18 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ageMax">Maximum Age</Label>
                <Input
                  id="ageMax"
                  type="number"
                  min="0"
                  max="120"
                  value={formData.ageMax}
                  onChange={(e) => setFormData({ ...formData, ageMax: parseInt(e.target.value) || 65 })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="geographicScope">Geographic Scope</Label>
                <Select
                  value={formData.geographicScope}
                  onValueChange={(value) => setFormData({ ...formData, geographicScope: value as any })}
                >
                  <SelectTrigger id="geographicScope">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="local">Local/Regional</SelectItem>
                    <SelectItem value="national">National</SelectItem>
                    <SelectItem value="international">International</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2 mt-8">
                <Switch
                  id="healthyVolunteers"
                  checked={formData.healthyVolunteers}
                  onCheckedChange={(checked) => setFormData({ ...formData, healthyVolunteers: checked })}
                />
                <Label htmlFor="healthyVolunteers">Include healthy volunteers</Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="specificPopulation">Specific Population Characteristics</Label>
              <Textarea
                id="specificPopulation"
                placeholder="Describe any specific population characteristics, disease severity requirements, etc..."
                rows={3}
                value={formData.specificPopulation}
                onChange={(e) => setFormData({ ...formData, specificPopulation: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Trial Assignment & Randomization */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Trial Assignment Method
                </CardTitle>
                <CardDescription>
                  Define how participants will be assigned to study arms
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("assignment-method")}
                onRefresh={() => handleGetInsights("assignment-method", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "assignment-method"}
                hasCachedData={!!cachedInsights["assignment-method"]}
                showRefresh={!!cachedInsights["assignment-method"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trialAssignmentMethod">Assignment Method</Label>
              <Select
                value={formData.trialAssignmentMethod}
                onValueChange={(value) => setFormData({ ...formData, trialAssignmentMethod: value })}
              >
                <SelectTrigger id="trialAssignmentMethod">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="randomization">Randomization</SelectItem>
                  <SelectItem value="stratification">Stratification</SelectItem>
                  <SelectItem value="randomization-stratification">Randomization with Stratification</SelectItem>
                  <SelectItem value="no-assignment">No Assignment (Single Arm)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Inclusion Criteria */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <UserCheck className="h-5 w-5" />
                  Inclusion Criteria
                </CardTitle>
                <CardDescription>
                  Define who can participate in the study
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("inclusion-exclusion")}
                onRefresh={() => handleGetInsights("inclusion-exclusion", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "inclusion-exclusion"}
                hasCachedData={!!cachedInsights["inclusion-exclusion"]}
                showRefresh={!!cachedInsights["inclusion-exclusion"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.inclusionCriteria.map((criteria, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                <span className="flex-1 text-sm">{criteria}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeInclusionCriteria(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add inclusion criterion..."
                value={newInclusionCriteria}
                onChange={(e) => setNewInclusionCriteria(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addInclusionCriteria())}
              />
              <Button onClick={addInclusionCriteria} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Exclusion Criteria */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <UserX className="h-5 w-5" />
                  Exclusion Criteria
                </CardTitle>
                <CardDescription>
                  Define who cannot participate in the study
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("exclusion-criteria")}
                onRefresh={() => handleGetInsights("exclusion-criteria", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "exclusion-criteria"}
                hasCachedData={!!cachedInsights["exclusion-criteria"]}
                showRefresh={!!cachedInsights["exclusion-criteria"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.exclusionCriteria.map((criteria, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                <span className="flex-1 text-sm">{criteria}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeExclusionCriteria(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add exclusion criterion..."
                value={newExclusionCriteria}
                onChange={(e) => setNewExclusionCriteria(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addExclusionCriteria())}
              />
              <Button onClick={addExclusionCriteria} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Site Strategy & Geographic Distribution */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Site Strategy & Geographic Distribution
                </CardTitle>
                <CardDescription>
                  Define site requirements and geographic coverage
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("site-strategy")}
                onRefresh={() => handleGetInsights("site-strategy", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "site-strategy"}
                hasCachedData={!!cachedInsights["site-strategy"]}
                showRefresh={!!cachedInsights["site-strategy"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="numberOfSites">Number of Sites</Label>
                <Input
                  id="numberOfSites"
                  placeholder="e.g., 15, 20-25"
                  value={formData.numberOfSites}
                  onChange={(e) => setFormData({ ...formData, numberOfSites: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="siteDistribution">Site Distribution Strategy</Label>
              <Textarea
                id="siteDistribution"
                placeholder="Describe the strategy for site selection and distribution (urban vs rural, academic vs community, etc.)..."
                rows={3}
                value={formData.siteDistribution}
                onChange={(e) => setFormData({ ...formData, siteDistribution: e.target.value })}
              />
            </div>

            {/* Countries & Sites */}
            <div className="space-y-3">
              <Label>Countries Engaged in Research</Label>
              {formData.countriesEngaged.map((country, index) => (
                <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                  <Globe className="h-4 w-4 text-gray-400" />
                  <span className="flex-1 text-sm">{country}</span>
                  {formData.sitesPerCountry[country] && (
                    <Badge variant="secondary">
                      {formData.sitesPerCountry[country]} sites
                    </Badge>
                  )}
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeCountry(country)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Country/Region name"
                  value={newCountry}
                  onChange={(e) => setNewCountry(e.target.value)}
                />
                <Input
                  placeholder="# sites (optional)"
                  type="number"
                  className="w-32"
                  value={newSiteCount}
                  onChange={(e) => setNewSiteCount(e.target.value)}
                />
                <Button onClick={addCountry} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {getTotalSites() > 0 && (
                <div className="rounded-lg bg-purple-50 dark:bg-purple-900/20 p-3">
                  <p className="text-sm font-medium text-purple-900 dark:text-purple-200">
                    Total: {getTotalSites()} sites across {Object.keys(formData.sitesPerCountry).length} countries with defined site counts
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Navigation */}
      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/study-design")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Study Design
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Safety Assessment"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewerPortal
        citations={(() => {
          // Extract all unique citations from all sections
          const allCitations = new Map<string, any>();
          Object.values(insightsData).forEach(data => {
            data.sections?.forEach((section: any) => {
              if (section.citations && Array.isArray(section.citations)) {
                section.citations.forEach((citation: any) => {
                  if (citation.id && !allCitations.has(citation.id)) {
                    allCitations.set(citation.id, citation);
                  }
                });
              }
            });
          });
          return Array.from(allCitations.values());
        })()}
        sources={(() => {
          // Extract all unique sources from all insights data
          const allSources = new Map<string, any>();
          Object.values(insightsData).forEach(data => {
            data.sources?.forEach((source: any) => {
              if (source.nctId && !allSources.has(source.nctId)) {
                allSources.set(source.nctId, source);
              }
            });
          });
          return Array.from(allSources.values());
        })()}
        onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
        currentStudyId={(() => {
          // Extract NCT ID from the current document URL
          if (!documentViewerUrl) return undefined;
          const match = documentViewerUrl.match(/NCT\\d+/i);
          return match ? match[0] : undefined;
        })()}
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}