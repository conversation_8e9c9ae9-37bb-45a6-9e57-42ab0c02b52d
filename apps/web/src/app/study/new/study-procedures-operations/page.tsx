"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Checkbox } from "~/components/ui/checkbox";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewerPortal } from "~/components/insights/DocumentViewerPortal";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  Calendar,
  Clock,
  TestTube,
  Microscope,
  Activity,
  Building2,
  TrendingUp,
  Database,
  Eye,
  Plus,
  X,
  Info
} from "lucide-react";

export default function StudyProceduresOperationsPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    // Timeline & Study Events (enhanced from existing)
    screeningPeriod: store.discovery.timeline?.screeningPeriod || "",
    baselinePeriod: store.discovery.timeline?.baselinePeriod || "",
    treatmentPeriod: store.discovery.timeline?.treatmentPeriod || "",
    followUpPeriod: store.discovery.timeline?.followUpPeriod || "",
    totalDuration: store.discovery.timeline?.totalDuration || "",
    studyEventsAndActivities: store.discovery.protocol?.studyEventsAndActivities || "",
    durationWithDates: store.discovery.protocol?.durationWithDates || "",
    trialInterventionDetails: store.discovery.protocol?.trialInterventionDetails || "",
    visits: store.discovery.timeline?.visits || [],
    
    // Laboratory & Biomarker Studies (7 new critical questions)
    willCollectBiologicalSamples: store.discovery.laboratory?.willCollectBiologicalSamples ?? false,
    biologicalSpecimens: store.discovery.laboratory?.biologicalSpecimens || [],
    collectionAndProcessing: store.discovery.laboratory?.collectionAndProcessing || "",
    willConductPK: store.discovery.laboratory?.willConductPK ?? false,
    willConductBiomarker: store.discovery.laboratory?.willConductBiomarker ?? false,
    willConductImmunogenicity: store.discovery.laboratory?.willConductImmunogenicity ?? false,
    willConductGeneticTesting: store.discovery.laboratory?.willConductGeneticTesting ?? false,
    
    // Operational Details (enhanced from existing)
    recruitmentRate: store.discovery.operational?.recruitmentRate || "",
    screenFailureRate: store.discovery.operational?.screenFailureRate || "",
    dropoutRate: store.discovery.operational?.dropoutRate || "",
    dataManagementSystem: store.discovery.operational?.dataManagementSystem || "edc",
    edcCTMSName: store.discovery.operational?.edcCTMSName || "",
    monitoringApproach: store.discovery.operational?.monitoringApproach || "risk-based",
  });

  const [newVisit, setNewVisit] = useState({ name: "", timepoint: "", procedures: [""], critical: false });
  const [newSpecimen, setNewSpecimen] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { sections: any[]; sources?: any[]; progressStatus?: string; progressMessages?: string[] }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || [],
        progressStatus: undefined,
        progressMessages: [],
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("study-procedures-operations");
      router.push("/study/new/regulatory-financial-legal");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setActiveInsightsPanel(field);
      return;
    }
    
    setActiveInsightsPanel(field);
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Analyzing study procedures...',
        progressMessages: [],
      }
    }));
    
    const progressUpdates = field === 'timeline-procedures' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching study timelines...' },
      { delay: 3500, message: 'Analyzing visit schedules...' },
      { delay: 6000, message: 'Extracting procedure patterns...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : field === 'laboratory-studies' ? [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching laboratory procedures...' },
      { delay: 3500, message: 'Analyzing biomarker strategies...' },
      { delay: 6000, message: 'Extracting PK/PD approaches...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ] : [
      { delay: 0, message: 'Initializing search...' },
      { delay: 1500, message: 'Searching operational data...' },
      { delay: 3500, message: 'Analyzing recruitment strategies...' },
      { delay: 6000, message: 'Extracting best practices...' },
      { delay: 8500, message: 'Generating recommendations...' },
    ];
    
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      drugName: store.discovery.intervention.name || undefined,
      targetEnrollment: store.discovery.population.targetEnrollment || undefined,
    };

    const queries: Record<string, string> = {
      "timeline-procedures": `What are typical visit schedules and procedures for ${store.discovery.condition || "clinical"} trials in ${store.discovery.phase || "Phase 2/3"}?`,
      "laboratory-studies": `What laboratory studies (PK, biomarkers, genetic testing) are commonly conducted in ${store.discovery.condition || "clinical"} trials?`,
      "operational-strategy": `What are typical recruitment rates and operational strategies for ${store.discovery.condition || "clinical"} trials with ${store.discovery.population.targetEnrollment || "300"} participants?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  // Visit management functions
  const addVisit = () => {
    if (newVisit.name.trim() && newVisit.timepoint.trim()) {
      setFormData(prev => ({
        ...prev,
        visits: [...prev.visits, {
          ...newVisit,
          procedures: newVisit.procedures.filter(p => p.trim())
        }]
      }));
      setNewVisit({ name: "", timepoint: "", procedures: [""], critical: false });
    }
  };

  const removeVisit = (index: number) => {
    setFormData(prev => ({
      ...prev,
      visits: prev.visits.filter((_, i) => i !== index)
    }));
  };

  const updateVisitProcedure = (index: number, value: string) => {
    const updatedProcedures = [...newVisit.procedures];
    updatedProcedures[index] = value;
    setNewVisit(prev => ({ ...prev, procedures: updatedProcedures }));
  };

  const addVisitProcedure = () => {
    setNewVisit(prev => ({ ...prev, procedures: [...prev.procedures, ""] }));
  };

  const removeVisitProcedure = (index: number) => {
    setNewVisit(prev => ({
      ...prev,
      procedures: prev.procedures.filter((_, i) => i !== index)
    }));
  };

  // Specimen management functions
  const addSpecimen = () => {
    if (newSpecimen.trim()) {
      setFormData(prev => ({
        ...prev,
        biologicalSpecimens: [...prev.biologicalSpecimens, newSpecimen.trim()]
      }));
      setNewSpecimen("");
    }
  };

  const removeSpecimen = (index: number) => {
    setFormData(prev => ({
      ...prev,
      biologicalSpecimens: prev.biologicalSpecimens.filter((_, i) => i !== index)
    }));
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Validation
    if (!formData.treatmentPeriod) {
      toast.error("Please specify the treatment period");
      return;
    }

    if (formData.willCollectBiologicalSamples && formData.biologicalSpecimens.length === 0) {
      toast.error("Since you're collecting biological samples, please specify what specimens will be collected");
      return;
    }

    if (formData.willCollectBiologicalSamples && !formData.collectionAndProcessing.trim()) {
      toast.error("Please describe the collection and processing procedures for biological specimens");
      return;
    }

    // Update store with comprehensive procedures and operations data
    store.updateDiscovery({ 
      timeline: {
        screeningPeriod: formData.screeningPeriod,
        baselinePeriod: formData.baselinePeriod,
        treatmentPeriod: formData.treatmentPeriod,
        followUpPeriod: formData.followUpPeriod,
        totalDuration: formData.totalDuration,
        visits: formData.visits,
      },
      protocol: {
        ...store.discovery.protocol,
        studyEventsAndActivities: formData.studyEventsAndActivities,
        durationWithDates: formData.durationWithDates,
        trialInterventionDetails: formData.trialInterventionDetails,
      },
      laboratory: {
        willCollectBiologicalSamples: formData.willCollectBiologicalSamples,
        biologicalSpecimens: formData.biologicalSpecimens,
        collectionAndProcessing: formData.collectionAndProcessing,
        willConductPK: formData.willConductPK,
        willConductBiomarker: formData.willConductBiomarker,
        willConductImmunogenicity: formData.willConductImmunogenicity,
        willConductGeneticTesting: formData.willConductGeneticTesting,
      },
      operational: {
        recruitmentRate: formData.recruitmentRate,
        screenFailureRate: formData.screenFailureRate,
        dropoutRate: formData.dropoutRate,
        dataManagementSystem: formData.dataManagementSystem as any,
        edcCTMSName: formData.edcCTMSName,
        monitoringApproach: formData.monitoringApproach as any,
      }
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        timeline: {
          screeningPeriod: formData.screeningPeriod,
          baselinePeriod: formData.baselinePeriod,
          treatmentPeriod: formData.treatmentPeriod,
          followUpPeriod: formData.followUpPeriod,
          totalDuration: formData.totalDuration,
          visits: formData.visits,
        },
        protocol: {
          studyEventsAndActivities: formData.studyEventsAndActivities,
          durationWithDates: formData.durationWithDates,
          trialInterventionDetails: formData.trialInterventionDetails,
        },
        laboratory: {
          willCollectBiologicalSamples: formData.willCollectBiologicalSamples,
          biologicalSpecimens: formData.biologicalSpecimens,
          collectionAndProcessing: formData.collectionAndProcessing,
          willConductPK: formData.willConductPK,
          willConductBiomarker: formData.willConductBiomarker,
          willConductImmunogenicity: formData.willConductImmunogenicity,
          willConductGeneticTesting: formData.willConductGeneticTesting,
        },
        operational: {
          recruitmentRate: formData.recruitmentRate,
          screenFailureRate: formData.screenFailureRate,
          dropoutRate: formData.dropoutRate,
          dataManagementSystem: formData.dataManagementSystem,
          edcCTMSName: formData.edcCTMSName,
          monitoringApproach: formData.monitoringApproach,
        }
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    if (actionableData) {
      const updates: any = {};
      
      if (field === "timeline-procedures") {
        if (actionableData.field === 'visits' && actionableData.visits) {
          updates.visits = [...formData.visits, ...actionableData.visits];
          toast.success("Visit schedule updated");
        }
        if (actionableData.field === 'treatmentPeriod') {
          updates.treatmentPeriod = actionableData.value;
          toast.success("Treatment period updated");
        }
      }
      
      if (field === "laboratory-studies") {
        if (actionableData.field === 'biologicalSpecimens' && actionableData.specimens) {
          updates.biologicalSpecimens = [...new Set([...formData.biologicalSpecimens, ...actionableData.specimens])];
          toast.success("Biological specimens updated");
        }
        if (actionableData.field === 'willConductPK') {
          updates.willConductPK = actionableData.value;
          toast.success("PK studies plan updated");
        }
      }
      
      if (field === "operational-strategy") {
        if (actionableData.field === 'recruitmentRate') {
          updates.recruitmentRate = actionableData.value;
          toast.success("Recruitment rate updated");
        }
        if (actionableData.field === 'edcCTMSName') {
          updates.edcCTMSName = actionableData.value;
          toast.success("EDC/CTMS system updated");
        }
      }
      
      if (Object.keys(updates).length > 0) {
        setFormData(prev => ({ ...prev, ...updates }));
      }
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Study Procedures & Operations
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Define study timeline, procedures, laboratory studies, and operational framework
          </p>
        </div>
      </BlurFade>

      {/* Study Timeline & Duration */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Study Timeline & Duration
                </CardTitle>
                <CardDescription>
                  Define study phases and overall duration with specific dates
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("timeline-procedures")}
                onRefresh={() => handleGetInsights("timeline-procedures", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "timeline-procedures"}
                hasCachedData={!!cachedInsights["timeline-procedures"]}
                showRefresh={!!cachedInsights["timeline-procedures"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="screeningPeriod">Screening Period</Label>
                <Input
                  id="screeningPeriod"
                  placeholder="e.g., 4 weeks, 30 days"
                  value={formData.screeningPeriod}
                  onChange={(e) => setFormData({ ...formData, screeningPeriod: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="baselinePeriod">Baseline Period</Label>
                <Input
                  id="baselinePeriod"
                  placeholder="e.g., 1 week, Day -7 to Day 0"
                  value={formData.baselinePeriod}
                  onChange={(e) => setFormData({ ...formData, baselinePeriod: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="treatmentPeriod">Treatment Period *</Label>
                <Input
                  id="treatmentPeriod"
                  placeholder="e.g., 24 weeks, 6 months"
                  value={formData.treatmentPeriod}
                  onChange={(e) => setFormData({ ...formData, treatmentPeriod: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="followUpPeriod">Follow-up Period</Label>
                <Input
                  id="followUpPeriod"
                  placeholder="e.g., 4 weeks, 30 days post-treatment"
                  value={formData.followUpPeriod}
                  onChange={(e) => setFormData({ ...formData, followUpPeriod: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="totalDuration">Total Study Duration</Label>
                <Input
                  id="totalDuration"
                  placeholder="e.g., 32 weeks, 8 months"
                  value={formData.totalDuration}
                  onChange={(e) => setFormData({ ...formData, totalDuration: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="durationWithDates">Duration with Specific Dates</Label>
                <Input
                  id="durationWithDates"
                  placeholder="e.g., Jan 2024 - Sep 2024"
                  value={formData.durationWithDates}
                  onChange={(e) => setFormData({ ...formData, durationWithDates: e.target.value })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Trial Intervention & Study Events */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Trial Intervention & Study Events
            </CardTitle>
            <CardDescription>
              Provide detailed descriptions of interventions and study activities
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="trialInterventionDetails">Describe the Trial Intervention</Label>
              <Textarea
                id="trialInterventionDetails"
                placeholder="Provide detailed intervention description including dosing, administration route, frequency, packaging, supply chain, etc..."
                rows={4}
                value={formData.trialInterventionDetails}
                onChange={(e) => setFormData({ ...formData, trialInterventionDetails: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="studyEventsAndActivities">Describe Study Events and Activities</Label>
              <Textarea
                id="studyEventsAndActivities"
                placeholder="Detail all study events, assessments, procedures, and activities that will occur during the study..."
                rows={5}
                value={formData.studyEventsAndActivities}
                onChange={(e) => setFormData({ ...formData, studyEventsAndActivities: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Visit Schedule */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Visit Schedule & Procedures
            </CardTitle>
            <CardDescription>
              Define the detailed visit schedule with specific procedures for each timepoint
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.visits.map((visit, index) => (
              <div key={index} className="rounded-lg border p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{visit.name}</h4>
                    <Badge variant="outline">{visit.timepoint}</Badge>
                    {visit.critical && (
                      <Badge variant="destructive" className="text-xs">Critical</Badge>
                    )}
                  </div>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeVisit(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground">
                  <strong>Procedures:</strong> {visit.procedures.join(", ")}
                </div>
              </div>
            ))}

            {/* Add New Visit */}
            <div className="rounded-lg border-2 border-dashed p-4 space-y-3">
              <h4 className="font-medium">Add Visit</h4>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  placeholder="Visit name (e.g., Baseline, Week 4)"
                  value={newVisit.name}
                  onChange={(e) => setNewVisit(prev => ({ ...prev, name: e.target.value }))}
                />
                <Input
                  placeholder="Timepoint (e.g., Day 0, Week 12)"
                  value={newVisit.timepoint}
                  onChange={(e) => setNewVisit(prev => ({ ...prev, timepoint: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Procedures</Label>
                {newVisit.procedures.map((procedure, pIndex) => (
                  <div key={pIndex} className="flex gap-2">
                    <Input
                      placeholder="Procedure (e.g., vital signs, blood draw, efficacy assessment)"
                      value={procedure}
                      onChange={(e) => updateVisitProcedure(pIndex, e.target.value)}
                    />
                    {pIndex > 0 && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => removeVisitProcedure(pIndex)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={addVisitProcedure}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Procedure
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="critical"
                  checked={newVisit.critical}
                  onCheckedChange={(checked) => setNewVisit(prev => ({ ...prev, critical: checked as boolean }))}
                />
                <Label htmlFor="critical" className="text-sm">Critical visit</Label>
              </div>
              
              <Button onClick={addVisit} className="w-full">
                Add Visit
              </Button>
            </div>
            
            <p className="text-xs text-muted-foreground">
              {formData.visits.length} visits defined, {formData.visits.filter(v => v.critical).length} critical
            </p>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Laboratory & Biomarker Studies */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TestTube className="h-5 w-5" />
                  Laboratory & Biomarker Studies
                </CardTitle>
                <CardDescription>
                  Define biological sample collection and specialized laboratory studies
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("laboratory-studies")}
                onRefresh={() => handleGetInsights("laboratory-studies", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "laboratory-studies"}
                hasCachedData={!!cachedInsights["laboratory-studies"]}
                showRefresh={!!cachedInsights["laboratory-studies"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="willCollectBiologicalSamples"
                checked={formData.willCollectBiologicalSamples}
                onCheckedChange={(checked) => setFormData({ ...formData, willCollectBiologicalSamples: checked })}
              />
              <Label htmlFor="willCollectBiologicalSamples" className="text-base">
                Will any biological samples be collected and used in this research?
              </Label>
            </div>

            {formData.willCollectBiologicalSamples && (
              <div className="space-y-4">
                <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-4 w-4 text-blue-600" />
                    <h4 className="font-medium text-blue-900 dark:text-blue-200">Biological Sample Collection Active</h4>
                  </div>
                  <p className="text-sm text-blue-800 dark:text-blue-300">
                    Please specify the specimens that will be collected and their processing requirements.
                  </p>
                </div>

                {/* Biological Specimens */}
                <div className="space-y-3">
                  <Label>Select All Biological Specimens to be Used</Label>
                  {formData.biologicalSpecimens.map((specimen, index) => (
                    <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                      <Microscope className="h-4 w-4 text-blue-500" />
                      <span className="flex-1 text-sm">{specimen}</span>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => removeSpecimen(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add specimen type (e.g., blood, urine, tissue, saliva)..."
                      value={newSpecimen}
                      onChange={(e) => setNewSpecimen(e.target.value)}
                      onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addSpecimen())}
                    />
                    <Button onClick={addSpecimen} size="icon">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="collectionAndProcessing">Scientifically Detail Collection and Processing</Label>
                  <Textarea
                    id="collectionAndProcessing"
                    placeholder="Describe collection procedures, processing requirements, storage conditions, shipment protocols, analysis plans, etc..."
                    rows={4}
                    value={formData.collectionAndProcessing}
                    onChange={(e) => setFormData({ ...formData, collectionAndProcessing: e.target.value })}
                  />
                </div>

                {/* Specialized Studies */}
                <div className="space-y-3">
                  <Label>Specialized Laboratory Studies</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="willConductPK"
                        checked={formData.willConductPK}
                        onCheckedChange={(checked) => setFormData({ ...formData, willConductPK: checked })}
                      />
                      <Label htmlFor="willConductPK" className="text-sm">
                        Pharmacokinetics (PK) studies
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="willConductBiomarker"
                        checked={formData.willConductBiomarker}
                        onCheckedChange={(checked) => setFormData({ ...formData, willConductBiomarker: checked })}
                      />
                      <Label htmlFor="willConductBiomarker" className="text-sm">
                        Biomarker testing
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="willConductImmunogenicity"
                        checked={formData.willConductImmunogenicity}
                        onCheckedChange={(checked) => setFormData({ ...formData, willConductImmunogenicity: checked })}
                      />
                      <Label htmlFor="willConductImmunogenicity" className="text-sm">
                        Immunogenicity tests
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="willConductGeneticTesting"
                        checked={formData.willConductGeneticTesting}
                        onCheckedChange={(checked) => setFormData({ ...formData, willConductGeneticTesting: checked })}
                      />
                      <Label htmlFor="willConductGeneticTesting" className="text-sm">
                        Genetic testing/sequencing
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Operational Strategy */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Operational Strategy
                </CardTitle>
                <CardDescription>
                  Define recruitment strategy, data management, and monitoring approach
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("operational-strategy")}
                onRefresh={() => handleGetInsights("operational-strategy", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "operational-strategy"}
                hasCachedData={!!cachedInsights["operational-strategy"]}
                showRefresh={!!cachedInsights["operational-strategy"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="recruitmentRate">Expected Recruitment Rate</Label>
                <Input
                  id="recruitmentRate"
                  placeholder="e.g., 10 participants/month"
                  value={formData.recruitmentRate}
                  onChange={(e) => setFormData({ ...formData, recruitmentRate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="screenFailureRate">Screen Failure Rate</Label>
                <Input
                  id="screenFailureRate"
                  placeholder="e.g., 30%, 25-35%"
                  value={formData.screenFailureRate}
                  onChange={(e) => setFormData({ ...formData, screenFailureRate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dropoutRate">Expected Dropout Rate</Label>
                <Input
                  id="dropoutRate"
                  placeholder="e.g., 15%, 10-20%"
                  value={formData.dropoutRate}
                  onChange={(e) => setFormData({ ...formData, dropoutRate: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dataManagementSystem">Data Management System</Label>
                <Select
                  value={formData.dataManagementSystem}
                  onValueChange={(value) => setFormData({ ...formData, dataManagementSystem: value as any })}
                >
                  <SelectTrigger id="dataManagementSystem">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="edc">Electronic Data Capture (EDC)</SelectItem>
                    <SelectItem value="paper">Paper-based</SelectItem>
                    <SelectItem value="hybrid">Hybrid (EDC + Paper)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="monitoringApproach">Monitoring Approach</Label>
                <Select
                  value={formData.monitoringApproach}
                  onValueChange={(value) => setFormData({ ...formData, monitoringApproach: value as any })}
                >
                  <SelectTrigger id="monitoringApproach">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="on-site">On-site Monitoring</SelectItem>
                    <SelectItem value="remote">Remote Monitoring</SelectItem>
                    <SelectItem value="risk-based">Risk-based Monitoring</SelectItem>
                    <SelectItem value="hybrid">Hybrid Approach</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edcCTMSName">EDC and/or CTMS Name</Label>
              <Input
                id="edcCTMSName"
                placeholder="e.g., Medidata Rave, Veeva Vault, REDCap, Custom System"
                value={formData.edcCTMSName}
                onChange={(e) => setFormData({ ...formData, edcCTMSName: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Navigation */}
      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/safety-assessment")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Safety Assessment
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Regulatory, Financial & Legal"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewerPortal
        citations={(() => {
          // Extract all unique citations from all sections
          const allCitations = new Map<string, any>();
          Object.values(insightsData).forEach(data => {
            data.sections?.forEach((section: any) => {
              if (section.citations && Array.isArray(section.citations)) {
                section.citations.forEach((citation: any) => {
                  if (citation.id && !allCitations.has(citation.id)) {
                    allCitations.set(citation.id, citation);
                  }
                });
              }
            });
          });
          return Array.from(allCitations.values());
        })()}
        sources={(() => {
          // Extract all unique sources from all insights data
          const allSources = new Map<string, any>();
          Object.values(insightsData).forEach(data => {
            data.sources?.forEach((source: any) => {
              if (source.nctId && !allSources.has(source.nctId)) {
                allSources.set(source.nctId, source);
              }
            });
          });
          return Array.from(allSources.values());
        })()}
        onStudySelect={(studyUrl) => setDocumentViewerUrl(studyUrl)}
        currentStudyId={(() => {
          // Extract NCT ID from the current document URL
          if (!documentViewerUrl) return undefined;
          const match = documentViewerUrl.match(/NCT\\d+/i);
          return match ? match[0] : undefined;
        })()}
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}