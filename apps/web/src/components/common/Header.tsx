"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "~/components/ui/button";
import { useTrialDesignStore } from "~/store/trial-design";
import { toast } from "sonner";
import { LogOut, Home, Plus } from "lucide-react";

export function Header() {
  const router = useRouter();
  const store = useTrialDesignStore();

  const handleNewSession = () => {
    store.resetSession();
    toast.success("Starting new trial design session");
    router.push("/study/new");
  };

  const handleSignOut = () => {
    // Clear the store
    store.resetStore();
    
    // Clear localStorage
    if (typeof window !== "undefined") {
      localStorage.removeItem("trial-design-storage");
    }
    
    toast.success("Session cleared");
    router.push("/");
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/80 backdrop-blur-sm">
      <div className="container flex h-14 items-center justify-between">
        <div className="flex items-center gap-6">
          <Link href="/" className="font-bold text-lg text-[#5A32FA]">
            TriaLynx
          </Link>
          
          <nav className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <Home className="mr-2 h-4 w-4" />
                Dashboard
              </Button>
            </Link>
            
            {store.sessionId && (
              <Button variant="ghost" size="sm" onClick={handleNewSession}>
                <Plus className="mr-2 h-4 w-4" />
                New Study
              </Button>
            )}
          </nav>
        </div>

        <div className="flex items-center gap-2">
          {store.sessionId && (
            <span className="text-xs text-gray-500">
              Session: {store.sessionId.slice(-8)}
            </span>
          )}
          
          <Button variant="outline" size="sm" onClick={handleSignOut}>
            <LogOut className="mr-2 h-4 w-4" />
            Clear Session
          </Button>
        </div>
      </div>
    </header>
  );
}