"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { DocumentViewer } from "./DocumentViewer";
import type { ComponentProps } from "react";

type DocumentViewerProps = ComponentProps<typeof DocumentViewer>;

export function DocumentViewerPortal(props: DocumentViewerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) {
    return null;
  }

  // Create a portal that renders the viewer at the document body level
  // This ensures it's not affected by any transforms in parent elements
  return createPortal(
    <DocumentViewer {...props} />,
    document.body
  );
}