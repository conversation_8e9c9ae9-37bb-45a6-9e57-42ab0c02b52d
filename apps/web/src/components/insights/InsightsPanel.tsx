"use client";

import { useState, useEffect, useRef } from "react";
import { X, ExternalLink, FileText, ChevronRight, ChevronDown, AlertCircle, Lightbulb, Info, Calculator, BookOpen } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";
import { cn } from "~/lib/utils";
import type { InsightSection, Citation, SourceDocument } from "~/types/trial-design";

interface InsightsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  loading?: boolean;
  error?: string;
  sections?: InsightSection[];
  sources?: SourceDocument[];
  onDocumentClick?: (url: string) => void;
  onApplySuggestion?: (suggestion: string, actionableData?: any) => void;
  onViewAllSources?: () => void;
  progressStatus?: string;
  progressMessages?: string[];
}

// Helper function to parse numbered alternative options
function parseAlternativeOptions(content: string): string[] {
  const lines = content.split('\n');
  const options: string[] = [];
  let currentOption = '';
  
  for (const line of lines) {
    // Check if line starts with a number followed by period or dot
    const numberMatch = line.match(/^(\d+)\.\s+(.+)/);
    if (numberMatch) {
      // If we have a previous option, save it
      if (currentOption) {
        options.push(currentOption.trim());
      }
      // Start new option
      currentOption = numberMatch[2] || '';
    } else if (currentOption && line.trim()) {
      // Continue current option if it spans multiple lines
      currentOption += ' ' + line.trim();
    }
  }
  
  // Don't forget the last option
  if (currentOption) {
    options.push(currentOption.trim());
  }
  
  return options;
}

// Helper function to render simple markdown (bold and italic)
function renderSimpleMarkdown(text: string): React.ReactNode {
  // Split by bold markers first
  const parts = text.split(/(\*\*[^*]+\*\*|\*[^*]+\*)/g);
  
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      // Bold text
      return <strong key={index}>{part.slice(2, -2)}</strong>;
    } else if (part.startsWith('*') && part.endsWith('*') && !part.startsWith('**')) {
      // Italic text
      return <em key={index}>{part.slice(1, -1)}</em>;
    } else {
      // Regular text
      return part;
    }
  });
}

export function InsightsPanel({
  isOpen,
  onClose,
  title,
  description,
  loading = false,
  error,
  sections = [],
  sources = [],
  onDocumentClick,
  onApplySuggestion,
  onViewAllSources,
  progressStatus,
  progressMessages = [],
}: InsightsPanelProps) {
  const [selectedSection, setSelectedSection] = useState<number>(0);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const savedScrollPosition = useRef<number>(0);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Lock body scroll when panel is open and restore position when closed
  useEffect(() => {
    if (isOpen) {
      // Save current scroll position immediately
      savedScrollPosition.current = window.scrollY;
      
      // Simple approach: just lock body scroll without position manipulation
      document.body.style.overflow = "hidden";
      
      // Scroll panel content to top
      setTimeout(() => {
        const panelContent = document.querySelector('.insights-panel-scroll-content');
        if (panelContent) {
          panelContent.scrollTop = 0;
        }
      }, 50);
    } else {
      // Restore body scroll
      document.body.style.overflow = "";
      
      if (savedScrollPosition.current !== undefined) {
        // Restore scroll position with delayed execution
        const scrollPosition = savedScrollPosition.current;
        
        // Use setTimeout to ensure DOM is ready
        setTimeout(() => {
          window.scrollTo({
            top: scrollPosition,
            behavior: 'instant'
          });
        }, 0);
      }
    }
    
    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  return (
    <>
      {/* Backdrop - Higher opacity for better contrast */}
      <div
        className={cn(
          "fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity z-40",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />

      {/* Panel - Light glassmorphic background for better readability */}
      <div
        className={cn(
          "fixed right-0 top-0 h-screen w-full md:w-[1200px] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-xl transition-transform duration-300 z-50 flex flex-col",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
      >
        <div className="flex flex-col h-full overflow-hidden">
          {/* Header - Light background with clear text - Stays fixed at top */}
          <div className="flex-shrink-0 border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 p-6">
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{title}</h2>
                {description && (
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    {description}
                  </p>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="ml-4"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content - Light background for readability */}
          <div className="flex-1 overflow-y-auto bg-white dark:bg-gray-900 insights-panel-scroll-content">
            <div className="px-6 py-4">
            {loading && (
              <div className="space-y-4">
                {/* Show progress status if available */}
                {progressStatus && (
                  <Card className="border-primary/20 bg-primary/5">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="animate-pulse">
                          <div className="h-2 w-2 bg-primary rounded-full animate-ping" />
                        </div>
                        <p className="text-sm font-medium text-primary">
                          {progressStatus}
                        </p>
                      </div>
                      {/* Show progress messages */}
                      {progressMessages.length > 0 && (
                        <div className="mt-3 space-y-1">
                          {progressMessages.slice(-3).map((message, i) => (
                            <p key={i} className="text-xs text-muted-foreground pl-5">
                              • {message}
                            </p>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
                {/* Show loading skeletons if no progress status */}
                {!progressStatus && (
                  <>
                    <Skeleton className="h-32 w-full" />
                    <Skeleton className="h-32 w-full" />
                    <Skeleton className="h-32 w-full" />
                  </>
                )}
              </div>
            )}

            {error && (
              <Card className="border-destructive">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-destructive">
                    <AlertCircle className="h-5 w-5" />
                    Error Loading Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{error}</p>
                </CardContent>
              </Card>
            )}

            {!loading && !error && sections.length > 0 && (
              <div className="pb-6">
                {/* Group sections by type */}
                {(() => {
                  const overview = sections.find(s => s.type === 'overview');
                  const recommendations = sections.filter(s => (s.type === 'recommendation' || s.actionable) && s.type !== 'overview');
                  const information = sections.filter(s => !s.actionable && s.type !== 'recommendation' && s.type !== 'overview');
                  
                  return (
                    <>
                      {/* Overview/Context if present - spans full width */}
                      {overview && (
                        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                          <p className="text-sm text-blue-900 dark:text-blue-100 leading-relaxed">
                            {overview.content}
                          </p>
                        </div>
                      )}

                      {/* Two-column grid layout */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* LEFT COLUMN: Primary Recommendations */}
                        <div className="space-y-4">
                          {/* Primary Recommendations */}
                          {recommendations.length > 0 && (
                            <div className="space-y-3">
                              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 sticky top-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur z-10 py-2 -mx-2 px-2 border-b border-gray-100 dark:border-gray-800">Recommendations</h3>
                          {recommendations.map((section, index) => {
                            // Parse alternative options if this is an alternatives section
                            const isAlternatives = section.type === 'alternatives' || section.title === 'Alternative Options';
                            const alternatives = isAlternatives ? parseAlternativeOptions(section.content) : null;
                            
                            // Check if this is an endpoints section with parsed endpoints
                            const isEndpointSection = section.type?.endsWith('-endpoints');
                            const hasEndpoints = section.endpoints && section.endpoints.length > 0;
                            const sectionId = `rec-${index}`;
                            const isExpanded = expandedSections.has(sectionId);
                            
                            // Collapsible sections for endpoints
                            if (isEndpointSection && hasEndpoints) {
                              return (
                                <Card key={sectionId} className="border-primary/20 overflow-hidden">
                                  <CardHeader 
                                    className="pb-3 cursor-pointer hover:bg-accent/50 transition-colors"
                                    onClick={() => toggleSection(sectionId)}
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <Lightbulb className="h-4 w-4 text-primary" />
                                        <CardTitle className="text-base">{section.title}</CardTitle>
                                        <Badge variant="secondary" className="text-xs">
                                          {section.endpoints!.length} endpoints
                                        </Badge>
                                      </div>
                                      {isExpanded ? (
                                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                      ) : (
                                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                                      )}
                                    </div>
                                  </CardHeader>
                                  {isExpanded && (
                                    <CardContent>
                                      <div className="space-y-3">
                                        {section.endpoints!.map((endpoint, epIndex) => (
                                          <div key={epIndex} className="border-l-2 border-primary/20 pl-3">
                                            <h4 className="font-medium text-sm mb-1">{endpoint.title}</h4>
                                            {endpoint.content && (
                                              <p className="text-xs text-muted-foreground mb-2 whitespace-pre-line">
                                                {endpoint.content}
                                              </p>
                                            )}
                                            {onApplySuggestion && endpoint.actionable && (
                                              <Button
                                                variant="default"
                                                size="sm"
                                                onClick={() => onApplySuggestion(endpoint.title)}
                                                className="text-xs"
                                              >
                                                Add This Endpoint
                                              </Button>
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    </CardContent>
                                  )}
                                </Card>
                              );
                            }
                            
                            // Regular card for non-endpoint sections
                            return (
                              <Card key={`rec-${index}`} className="border-primary/20">
                                <CardHeader className="pb-3">
                                  <div className="flex items-center gap-2">
                                    <Lightbulb className="h-4 w-4 text-primary" />
                                    <CardTitle className="text-base">{section.title}</CardTitle>
                                  </div>
                                </CardHeader>
                                <CardContent>
                                  {isAlternatives && (section.alternatives || alternatives) ? (
                                    <div className="space-y-3">
                                      {/* Use structured alternatives if available, otherwise fall back to parsed */}
                                      {section.alternatives ? (
                                        // Use the structured alternatives with actionableData
                                        section.alternatives.map((alt: any, altIndex: number) => (
                                          <div key={altIndex} className="border-l-2 border-primary/20 pl-3">
                                            <div className="text-sm leading-relaxed mb-2">
                                              {renderSimpleMarkdown(`${altIndex + 1}. **${alt.endpoint}**`)}
                                              {alt.measurementMethod && (
                                                <div className="mt-1 text-muted-foreground text-xs">
                                                  <span className="font-medium">Measurement:</span> {alt.measurementMethod}
                                                </div>
                                              )}
                                              {alt.rationale && (
                                                <div className="mt-1 text-muted-foreground text-xs">
                                                  {alt.rationale}
                                                </div>
                                              )}
                                            </div>
                                            {onApplySuggestion && alt.actionableData && (
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onApplySuggestion(alt.endpoint, alt.actionableData)}
                                                className="text-xs"
                                              >
                                                Apply Option {altIndex + 1}
                                              </Button>
                                            )}
                                          </div>
                                        ))
                                      ) : (
                                        // Fall back to parsed alternatives for backwards compatibility
                                        alternatives?.map((alt, altIndex) => (
                                          <div key={altIndex} className="border-l-2 border-primary/20 pl-3">
                                            <div className="text-sm leading-relaxed mb-2">
                                              {renderSimpleMarkdown(alt)}
                                            </div>
                                            {onApplySuggestion && (
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onApplySuggestion(alt)}
                                                className="text-xs"
                                              >
                                                Apply Option {altIndex + 1}
                                              </Button>
                                            )}
                                          </div>
                                        ))
                                      )}
                                    </div>
                                  ) : (
                                    <>
                                      {/* Check if section has individual criteria items for hover-to-apply */}
                                      {section.criteriaItems && section.criteriaItems.length > 0 ? (
                                        <div className="space-y-1 mb-3">
                                          {section.criteriaItems.map((item: any, itemIndex: number) => (
                                            <div
                                              key={itemIndex}
                                              className="group relative cursor-pointer rounded-lg py-2 px-3 transition-all duration-200 hover:bg-primary/5 hover:shadow-sm border border-transparent hover:border-primary/20"
                                              onClick={() => onApplySuggestion && onApplySuggestion(item.text, item.actionableData)}
                                            >
                                              {/* Strengthened glassy overlay on hover */}
                                              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 to-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-200 backdrop-blur-sm" />
                                              
                                              {/* Criterion text */}
                                              <div className="relative text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                                                • {item.text}
                                              </div>
                                              
                                              {/* Centered watermark overlay - appears on hover */}
                                              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                                <div className="opacity-0 group-hover:opacity-90 transition-opacity duration-200 text-lg font-bold text-white drop-shadow-lg">
                                                  Click to apply
                                                </div>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      ) : section.countryItems && section.countryItems.length > 0 ? (
                                        <div className="space-y-1 mb-3">
                                          {section.countryItems.map((item: any, itemIndex: number) => (
                                            <div
                                              key={itemIndex}
                                              className="group relative cursor-pointer rounded-lg py-2 px-3 transition-all duration-200 hover:bg-primary/5 hover:shadow-sm border border-transparent hover:border-primary/20"
                                              onClick={() => onApplySuggestion && onApplySuggestion(item.text, item.actionableData)}
                                            >
                                              {/* Strengthened glassy overlay on hover */}
                                              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 to-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-200 backdrop-blur-sm" />
                                              
                                              {/* Country recommendation text */}
                                              <div className="relative text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                                                🌍 {item.text}
                                              </div>
                                              
                                              {/* Centered watermark overlay - appears on hover */}
                                              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                                <div className="opacity-0 group-hover:opacity-90 transition-opacity duration-200 text-lg font-bold text-white drop-shadow-lg">
                                                  Click to apply
                                                </div>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      ) : section.strategicItems && section.strategicItems.length > 0 ? (
                                        <div className="space-y-1 mb-3">
                                          {section.strategicItems.map((item: any, itemIndex: number) => (
                                            <div
                                              key={itemIndex}
                                              className="group relative cursor-pointer rounded-lg py-2 px-3 transition-all duration-200 hover:bg-primary/5 hover:shadow-sm border border-transparent hover:border-primary/20"
                                              onClick={() => onApplySuggestion && onApplySuggestion(item.text, item.actionableData)}
                                            >
                                              {/* Strengthened glassy overlay on hover */}
                                              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 to-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-200 backdrop-blur-sm" />
                                              
                                              {/* Strategic recommendation text */}
                                              <div className="relative text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                                                💡 {item.text}
                                              </div>
                                              
                                              {/* Centered watermark overlay - appears on hover */}
                                              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                                <div className="opacity-0 group-hover:opacity-90 transition-opacity duration-200 text-lg font-bold text-white drop-shadow-lg">
                                                  Click to apply
                                                </div>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      ) : (
                                        <div className="prose prose-sm max-w-none mb-3">
                                          <div className="text-sm leading-relaxed whitespace-pre-line">
                                            {renderSimpleMarkdown(section.content)}
                                          </div>
                                        </div>
                                      )}
                                      
                                      {onApplySuggestion && section.actionable !== false && !section.type?.includes('overview') && (
                                        <Button
                                          variant="default"
                                          size="sm"
                                          onClick={() => onApplySuggestion(section.content, section.actionableData)}
                                          className="w-full"
                                        >
                                          {section.criteriaItems ? 
                                            (section.title === 'Recommended Inclusion Criteria' ? 'Apply All Priority Criteria' : 'Apply All Criteria') 
                                            : section.countryItems ? 'Apply All Countries' 
                                            : section.strategicItems ? 'Apply All Strategies' 
                                            : 'Apply This Recommendation'}
                                        </Button>
                                      )}
                                    </>
                                  )}
                                </CardContent>
                              </Card>
                            );
                              })}
                            </div>
                          )}
                        </div>

                        {/* RIGHT COLUMN: Supporting Information */}
                        <div className="space-y-4">
                          {/* Supporting Information - Collapsible */}
                          {information.length > 0 && (
                            <div className="space-y-3">
                              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 sticky top-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur z-10 py-2 -mx-2 px-2 border-b border-gray-100 dark:border-gray-800">Supporting Information</h3>
                              <div className="space-y-2">
                            {information.map((section, index) => {
                              const sectionId = `info-${index}`;
                              const isExpanded = expandedSections.has(sectionId);
                              
                              return (
                                <Card key={sectionId} className="border-muted overflow-hidden">
                                  <CardHeader 
                                    className="pb-3 cursor-pointer hover:bg-accent/50 transition-colors"
                                    onClick={() => toggleSection(sectionId)}
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        {section.type === 'calculation' && <Calculator className="h-4 w-4 text-muted-foreground" />}
                                        {section.type === 'references' && <BookOpen className="h-4 w-4 text-muted-foreground" />}
                                        {section.type === 'rationale' && <Info className="h-4 w-4 text-muted-foreground" />}
                                        {section.type === 'details' && <FileText className="h-4 w-4 text-muted-foreground" />}
                                        {section.type === 'alternatives' && <Lightbulb className="h-4 w-4 text-muted-foreground" />}
                                        {(!section.type || section.type === 'information') && <Info className="h-4 w-4 text-muted-foreground" />}
                                        <CardTitle className="text-sm font-medium">{section.title}</CardTitle>
                                      </div>
                                      {isExpanded ? (
                                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                      ) : (
                                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                                      )}
                                    </div>
                                  </CardHeader>
                                  {isExpanded && (
                                    <CardContent className="pt-0">
                                      <div className="text-sm text-muted-foreground whitespace-pre-line">
                                        {renderSimpleMarkdown(section.content)}
                                      </div>
                                      {section.type === 'alternatives' && onApplySuggestion && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => onApplySuggestion(section.content, section.actionableData)}
                                          className="w-full mt-3"
                                        >
                                          Apply Alternative
                                        </Button>
                                      )}
                                    </CardContent>
                                  )}
                                </Card>
                              );
                            })}
                              </div>
                            </div>
                          )}

                          {/* Citations from the response */}
                      {(() => {
                        // Collect all unique citations from all sections
                        const allCitations = new Map<string, any>();
                        sections.forEach(section => {
                          if (section.citations && Array.isArray(section.citations)) {
                            section.citations.forEach((citation: any) => {
                              if (citation.id && !allCitations.has(citation.id)) {
                                allCitations.set(citation.id, citation);
                              }
                            });
                          }
                        });
                        
                        const uniqueCitations = Array.from(allCitations.values());
                        
                        if (uniqueCitations.length > 0) {
                          return (
                            <div className="space-y-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 sticky top-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur z-10 py-2 -mx-2 px-2 border-b border-gray-100 dark:border-gray-800">
                                Supporting Studies Referenced
                              </h3>
                              <div className="space-y-2">
                                {uniqueCitations.map((citation, index) => (
                                  <Card 
                                    key={`citation-${index}`} 
                                    className="border-muted hover:border-primary/50 transition-colors cursor-pointer"
                                    onClick={() => {
                                      if (citation.url && onDocumentClick) {
                                        // Convert the regular URL to S3 URI format if needed
                                        const s3Uri = citation.url.includes('s3://') ? citation.url : 
                                                     citation.url.includes('clinicaltrials.gov') ? 
                                                     `s3://trialynx-clinical-trials-gov/text-documents/${citation.id.substring(0, 6)}/${citation.id}.txt` :
                                                     citation.url;
                                        onDocumentClick(s3Uri);
                                      }
                                    }}
                                  >
                                    <CardContent className="p-4">
                                      <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center gap-2">
                                            <BookOpen className="h-4 w-4 text-primary" />
                                            <span className="text-sm font-semibold text-primary">
                                              {citation.id}
                                            </span>
                                          </div>
                                        </div>
                                        {citation.title && (
                                          <p className="text-xs text-muted-foreground line-clamp-2">
                                            {citation.title}
                                          </p>
                                        )}
                                      </div>
                                    </CardContent>
                                  </Card>
                                ))}
                              </div>
                            </div>
                          );
                        }
                        return null;
                      })()}

                          {/* Source Documents */}
                          {sources.length > 0 && (
                            <div className="space-y-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 sticky top-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur z-10 py-2 -mx-2 px-2 border-b border-gray-100 dark:border-gray-800">
                                Source Studies
                              </h3>
                              <div className="space-y-2">
                                {sources.map((source, index) => (
                                  <Card 
                                    key={`source-${index}`} 
                                    className="border-muted hover:border-primary/50 transition-colors cursor-pointer"
                                    onClick={() => onDocumentClick?.(source.s3Uri)}
                                  >
                                <CardContent className="p-4">
                                  <div className="space-y-2">
                                    {/* NCT ID and badges */}
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <FileText className="h-4 w-4 text-primary" />
                                        <span className="text-sm font-semibold text-primary">
                                          {source.nctId}
                                        </span>
                                      </div>
                                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                                    </div>
                                    
                                    {/* Status and Study Type badges - only show if meaningful */}
                                    {(source.status !== 'Unknown' || source.studyType !== 'Unknown') && (
                                      <div className="flex gap-2 flex-wrap">
                                        {source.status !== 'Unknown' && (
                                          <Badge 
                                            variant={source.status === 'COMPLETED' ? 'default' : 
                                                    source.status === 'RECRUITING' ? 'secondary' : 
                                                    'outline'}
                                            className="text-xs"
                                          >
                                            {source.status}
                                          </Badge>
                                        )}
                                        {source.studyType !== 'Unknown' && (
                                          <Badge variant="outline" className="text-xs">
                                            {source.studyType}
                                          </Badge>
                                        )}
                                      </div>
                                    )}
                                    
                                    {/* Content preview if meaningful */}
                                    {source.excerpt && (
                                      <p className="text-xs text-muted-foreground line-clamp-2 pt-1">
                                        {source.excerpt}
                                      </p>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            )}

            {!loading && !error && sections.length === 0 && (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">
                    No insights available yet. Try adjusting your query or providing more information.
                  </p>
                </CardContent>
              </Card>
            )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}