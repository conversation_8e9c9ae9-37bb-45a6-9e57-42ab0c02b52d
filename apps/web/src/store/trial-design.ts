import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { 
  Discovery<PERSON>ata, 
  StudyResult, 
  GeneratedInsights,
  StudyType,
  StudyPhase,
  Gender
} from "~/types/trial-design";

interface CompletedStudy {
  id: string;
  title: string;
  completedAt: string;
  synopsis: string;
  discovery: TrialDesignState["discovery"];
  phase?: string;
}

interface TrialDesignState {
  // Session Management
  sessionId: string | null;
  currentStep: number;
  completedSteps: string[];
  
  // Discovery Data - using comprehensive types
  discovery: {
    studyType: StudyType | null;
    phase: StudyPhase | null;
    condition: string;
    
    // Step 2: Investigational Product (11 questions)
    intervention: {
      // Basic info
      name?: string;
      category?: string;
      mechanism?: string;
      class?: string;
      drugClass?: string;
      isNewCompound?: boolean;
      deviceClass?: string;
      
      // Enhanced drug product information (new)
      medicalProblem?: string;
      comparisonToExistingTreatments?: string;
      regulatoryStatus?: string;
      activeIngredients?: string[];
      inactiveIngredients?: string[];
      ingredientRationale?: string;
      preclinicalStudies?: string;
      toxicityStudies?: string;
      clinicalTrialsHistory?: string;
      keyFindings?: string;
      pregnancySafety?: 'safe' | 'unsafe' | 'unknown' | 'contraindicated';
      pregnancySafetyDetails?: string;
      fdaApprovalStatus?: 'approved' | 'investigational' | 'compassionate' | 'off-label';
      drugCompoundNumber?: string;
      indApplicationNumber?: string;
    };
    
    // Step 1: Study Overview & Background (6 questions)
    protocol?: {
      protocolAcronym?: string;
      protocolFullTitle?: string;
      studyBackground?: string;
      studyDetailsForAI?: string;
      protocolIdNumber?: string;
      trialInterventionDetails?: string;
      studyEventsAndActivities?: string;
      durationWithDates?: string;
    };
    
    // Step 3: Study Design & Statistical Analysis (19 questions)
    design?: {
      // Design descriptors
      designDescriptors?: string[];
      designType?: string;
      randomizationRatio?: string;
      blinding?: string;
      controlType?: string;
      
      // Enhanced design details
      interventionModels?: string[];
      hasActiveComparator?: boolean;
      controlMethods?: string[];
      numberOfStudyArms?: number;
      studyArmDescriptions?: string[];
      analysisPopulations?: string[];
      hasAdaptiveDesign?: boolean;
      adaptiveDesignDetails?: string;
    };
    
    statistical?: {
      sampleSizeDetermination?: string;
      statisticalModel?: string;
      hypothesisAndAnalysis?: string;
      interimAnalysisPlan?: string;
      power?: number;
      alpha?: number;
      multipleTesting?: string;
      missingDataStrategy?: string;
    };
    
    // Step 4: Study Population (8 questions)  
    population: {
      // Basic demographics
      ageMin?: number;
      ageMax?: number;
      gender: Gender;
      specificPopulation?: string;
      inclusionCriteria: string[];
      exclusionCriteria: string[];
      healthyVolunteers?: boolean;
      
      // Enhanced population details
      targetEnrollment?: string;
      geographicScope?: "local" | "national" | "international";
      trialAssignmentMethod?: string;
      numberOfSites?: string;
      siteDistribution?: string;
      countriesEngaged?: string[];
      sitesPerCountry?: Record<string, number>;
    };
    
    // Step 5: Safety Assessment (6 questions)
    safety?: {
      willCollectAESAE?: boolean;
      likelySideEffects?: string[];
      lessLikelySideEffects?: string[];
      rareButSeriousSideEffects?: string[];
      hasReproductiveRisks?: boolean;
      reproductiveRiskDetails?: string;
    };
    
    // Step 6a: Study Procedures & Timeline
    timeline?: {
      screeningPeriod?: string;
      baselinePeriod?: string;
      treatmentPeriod?: string;
      followUpPeriod?: string;
      totalDuration?: string;
      visits?: Array<{
        name: string;
        timepoint: string;
        procedures: string[];
        critical?: boolean;
      }>;
    };
    
    // Step 6b: Laboratory & Biomarker Studies (7 questions)
    laboratory?: {
      willCollectBiologicalSamples?: boolean;
      biologicalSpecimens?: string[];
      collectionAndProcessing?: string;
      willConductPK?: boolean;
      willConductBiomarker?: boolean;
      willConductImmunogenicity?: boolean;
      willConductGeneticTesting?: boolean;
    };
    
    // Step 6c: Operational Details
    operational?: {
      recruitmentRate?: string;
      screenFailureRate?: string;
      dropoutRate?: string;
      dataManagementSystem?: "edc" | "paper" | "hybrid";
      edcCTMSName?: string;
      monitoringApproach?: "on-site" | "remote" | "risk-based" | "hybrid";
    };
    
    // Enhanced objectives with formal statements
    objectives: {
      primaryGoal: string;
      keyOutcome: string;
      secondaryGoals?: string[];
      studyDuration?: string;
      followUpPeriod?: string;
      primaryAspectAssessing?: string[];
      primaryObjectiveStatement?: string;
      secondaryObjectiveStatement?: string;
      exploratoryObjectives?: string[];
      exploratoryObjectiveAnalyses?: string[];
    };
    
    // Step 7: Regulatory, Financial & Legal (10 questions)
    regulatory?: {
      // Regulatory
      irbEthicsCommitteeName?: string;
      sponsorName?: string;
      drugManufacturerName?: string;
      drugManufacturerAddress?: string;
      willUseCRO?: boolean;
      croNameAndAddress?: string;
      dataEvaluationCommittees?: string[];
      independentCommittees?: string[];
      
      // Financial
      willCompensateParticipants?: boolean;
      compensationDetails?: string;
      whoWillPay?: string[];
    };
  };
  
  // Knowledge Base Results
  similarStudies: StudyResult[];
  selectedStudies: string[];
  isLoadingStudies: boolean;
  
  // Insights
  insights: GeneratedInsights | null;
  isGeneratingInsights: boolean;
  
  // Synopsis
  synopsis: string | null;
  
  // Completed Studies
  completedStudies: CompletedStudy[];
  
  // Insights Cache
  insightsCache: Record<string, any>;
  documentCache: Record<string, any>;
  
  // Actions
  setSessionId: (id: string) => void;
  setCurrentStep: (step: number) => void;
  markStepCompleted: (step: string) => void;
  updateDiscovery: (data: Partial<TrialDesignState["discovery"]>) => void;
  setSimilarStudies: (studies: StudyResult[]) => void;
  toggleStudySelection: (studyId: string) => void;
  setInsights: (insights: GeneratedInsights) => void;
  setSynopsis: (synopsis: string) => void;
  saveCompletedStudy: (title: string, synopsis: string) => void;
  cacheInsights: (key: string, data: any) => void;
  cacheDocument: (url: string, data: any) => void;
  resetStore: () => void;
  resetSession: () => void;
}

const initialState = {
  sessionId: null,
  currentStep: 0,
  completedSteps: [],
  discovery: {
    studyType: null,
    phase: null,
    condition: "",
    intervention: {},
    protocol: {},
    design: {},
    statistical: {},
    population: {
      gender: "all" as const,
      inclusionCriteria: [],
      exclusionCriteria: [],
      countriesEngaged: [],
    },
    safety: {
      likelySideEffects: [],
      lessLikelySideEffects: [],
      rareButSeriousSideEffects: [],
    },
    timeline: {
      visits: [],
    },
    laboratory: {
      biologicalSpecimens: [],
    },
    operational: {},
    objectives: {
      primaryGoal: "",
      keyOutcome: "",
      primaryAspectAssessing: [],
      exploratoryObjectives: [],
      exploratoryObjectiveAnalyses: [],
    },
    regulatory: {
      dataEvaluationCommittees: [],
      independentCommittees: [],
      whoWillPay: [],
    },
  },
  similarStudies: [],
  selectedStudies: [],
  isLoadingStudies: false,
  insights: null,
  isGeneratingInsights: false,
  synopsis: null,
  completedStudies: [],
  insightsCache: {},
  documentCache: {},
};

export const useTrialDesignStore = create<TrialDesignState>()(
  persist(
    (set) => ({
      ...initialState,
      
      setSessionId: (id) => set({ sessionId: id }),
      
      setCurrentStep: (step) => set({ currentStep: step }),
      
      markStepCompleted: (step) =>
        set((state) => ({
          completedSteps: [...new Set([...state.completedSteps, step])],
        })),
      
      updateDiscovery: (data) =>
        set((state) => ({
          discovery: {
            ...state.discovery,
            ...data,
          },
        })),
      
      setSimilarStudies: (studies) =>
        set({
          similarStudies: studies,
          isLoadingStudies: false,
        }),
      
      toggleStudySelection: (studyId) =>
        set((state) => ({
          selectedStudies: state.selectedStudies.includes(studyId)
            ? state.selectedStudies.filter((id) => id !== studyId)
            : [...state.selectedStudies, studyId],
        })),
      
      setInsights: (insights) =>
        set({
          insights,
          isGeneratingInsights: false,
        }),
      
      setSynopsis: (synopsis) => set({ synopsis }),
      
      saveCompletedStudy: (title, synopsis) =>
        set((state) => ({
          completedStudies: [
            ...state.completedStudies,
            {
              id: `study-${Date.now()}`,
              title,
              completedAt: new Date().toISOString(),
              synopsis,
              discovery: state.discovery,
              phase: state.discovery.phase,
            },
          ],
        })),
      
      cacheInsights: (key, data) =>
        set((state) => ({
          insightsCache: {
            ...state.insightsCache,
            [key]: data,
          },
        })),
      
      cacheDocument: (url, data) =>
        set((state) => ({
          documentCache: {
            ...state.documentCache,
            [url]: data,
          },
        })),
      
      resetStore: () => set(initialState),
      
      resetSession: () => set({
        sessionId: null,
        currentStep: 0,
        completedSteps: [],
        selectedStudies: [],
        insights: null,
      }),
    }),
    {
      name: "trial-design-storage",
      partialize: (state) => ({
        sessionId: state.sessionId,
        currentStep: state.currentStep,
        completedSteps: state.completedSteps,
        discovery: state.discovery,
        selectedStudies: state.selectedStudies,
        synopsis: state.synopsis,
        completedStudies: state.completedStudies,
      }),
    }
  )
);