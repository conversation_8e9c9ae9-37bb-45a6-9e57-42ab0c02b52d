/* Safari-specific fixes for dropdown select components */

/* Fix for Safari select dropdowns appearing with black background */
@supports (-webkit-touch-callout: none) {
  /* Safari only styles */
  
  /* Fix the trigger button */
  [data-slot="select-trigger"] {
    background-color: white !important;
    color: rgb(15, 23, 42) !important; /* text-slate-900 */
  }
  
  /* Fix the dropdown content */
  [data-slot="select-content"] {
    background-color: white !important;
    color: rgb(15, 23, 42) !important;
  }
  
  /* Fix the select items */
  [data-slot="select-item"] {
    background-color: transparent !important;
    color: rgb(15, 23, 42) !important;
  }
  
  /* Fix hover states */
  [data-slot="select-item"]:hover,
  [data-slot="select-item"]:focus {
    background-color: rgb(243, 244, 246) !important; /* gray-100 */
    color: rgb(15, 23, 42) !important;
  }
  
  /* Fix selected item with data-state */
  [data-slot="select-item"][data-state="checked"] {
    background-color: rgb(239, 246, 255) !important; /* blue-50 */
    color: rgb(59, 130, 246) !important; /* blue-500 */
  }
  
  /* Fix the select value text */
  [data-slot="select-value"] {
    color: rgb(15, 23, 42) !important;
  }
  
  /* Ensure dark mode doesn't override in Safari */
  @media (prefers-color-scheme: dark) {
    [data-slot="select-trigger"] {
      background-color: white !important;
      color: rgb(15, 23, 42) !important;
    }
    
    [data-slot="select-content"] {
      background-color: white !important;
      color: rgb(15, 23, 42) !important;
    }
    
    [data-slot="select-item"] {
      color: rgb(15, 23, 42) !important;
    }
  }
}

/* Additional Safari-specific form fixes */
@supports (-webkit-touch-callout: none) {
  /* Fix Safari autofill background colors */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-text-fill-color: rgb(15, 23, 42) !important;
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;
    box-shadow: 0 0 0px 1000px white inset !important;
    transition: background-color 5000s ease-in-out 0s;
  }
}