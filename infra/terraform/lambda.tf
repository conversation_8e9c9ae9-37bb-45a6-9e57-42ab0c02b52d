# IAM Role for Lambda
resource "aws_iam_role" "lambda_role" {
  name               = "${local.project_name}-${var.environment}-lambda-role"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role.json
  
  tags = local.common_tags
}

# IAM Policy for Lambda
resource "aws_iam_role_policy" "lambda_policy" {
  name   = "${local.project_name}-${var.environment}-lambda-policy"
  role   = aws_iam_role.lambda_role.id
  policy = data.aws_iam_policy_document.lambda_policy.json
}

# Lambda function for querying knowledge base
resource "aws_lambda_function" "query_knowledge_base" {
  filename         = "../../apps/lambda/dist/query-knowledge-base.js"
  function_name    = local.lambda_function_name
  role            = aws_iam_role.lambda_role.arn
  handler         = "query-knowledge-base.handler"
  runtime         = "nodejs20.x"
  timeout         = 30
  memory_size     = 512
  
  environment {
    variables = local.lambda_environment
  }
  
  source_code_hash = filebase64sha256("../../apps/lambda/dist/query-knowledge-base.js")
  
  tags = local.common_tags
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/${local.lambda_function_name}"
  retention_in_days = 7
  
  tags = local.common_tags
}

# Lambda permission for API Gateway
resource "aws_lambda_permission" "api_gateway" {
  statement_id  = "AllowAPIGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.query_knowledge_base.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/*/*"
}

# JWT Authorizer Lambda function
resource "aws_lambda_function" "jwt_authorizer" {
  filename         = "../../apps/lambda/dist/jwt-authorizer.js"
  function_name    = "${local.project_name}-${var.environment}-jwt-authorizer"
  role            = aws_iam_role.lambda_role.arn
  handler         = "jwt-authorizer.handler"
  runtime         = "nodejs20.x"
  timeout         = 10
  memory_size     = 256
  
  environment {
    variables = {
      AWS_REGION     = var.aws_region
      JWT_SECRET_ARN = "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:${var.jwt_secret_name}"
    }
  }
  
  source_code_hash = filebase64sha256("../../apps/lambda/dist/jwt-authorizer.js")
  
  tags = local.common_tags
}

# CloudWatch Log Group for JWT Authorizer Lambda
resource "aws_cloudwatch_log_group" "jwt_authorizer_logs" {
  name              = "/aws/lambda/${local.project_name}-${var.environment}-jwt-authorizer"
  retention_in_days = 7
  
  tags = local.common_tags
}

# Lambda permission for API Gateway Authorizer
resource "aws_lambda_permission" "api_gateway_authorizer" {
  statement_id  = "AllowAPIGatewayAuthorizerInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jwt_authorizer.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/authorizers/${aws_api_gateway_authorizer.jwt.id}"
}