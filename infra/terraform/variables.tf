variable "aws_region" {
  description = "AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project_owner" {
  description = "Email of the project owner"
  type        = string
  default     = "<EMAIL>"
}

variable "bedrock_knowledge_base_id" {
  description = "ID of the Bedrock knowledge base"
  type        = string
  sensitive   = true
}

variable "bedrock_model_id" {
  description = "Model ID for Bedrock inference"
  type        = string
  default     = "anthropic.claude-v2"
}

variable "cors_allowed_origins" {
  description = "Allowed origins for CORS"
  type        = list(string)
  default     = ["http://localhost:3000"]
}

variable "jwt_secret_name" {
  description = "Name of the JWT secret in AWS Secrets Manager"
  type        = string
  default     = "trialynx-jwt-secret"
}